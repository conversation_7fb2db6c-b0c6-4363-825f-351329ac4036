{"name": "cli-truncate", "version": "3.1.0", "description": "Truncate a string to a specific width in the terminal", "license": "MIT", "repository": "sindresorhus/cli-truncate", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["truncate", "ellipsis", "text", "limit", "slice", "cli", "terminal", "term", "shell", "width", "ansi", "string"], "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}