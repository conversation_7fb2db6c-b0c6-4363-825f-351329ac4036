{"name": "ink", "version": "4.4.1", "description": "React for CLI", "license": "MIT", "repository": "vadimdemedes/ink", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><EMAIL>", "url": "https://github.com/vadimdemedes"}, "type": "module", "exports": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "engines": {"node": ">=14.16"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "prepare": "npm run build", "test": "tsc --noEmit && xo && FORCE_COLOR=true ava", "example": "NODE_NO_WARNINGS=1 node --loader ts-node/esm", "benchmark": "NODE_NO_WARNINGS=1 node --loader ts-node/esm"}, "files": ["build"], "keywords": ["react", "cli", "jsx", "stdout", "components", "command-line", "preact", "redux", "print", "render", "colors", "text"], "dependencies": {"@alcalzone/ansi-tokenize": "^0.1.3", "ansi-escapes": "^6.0.0", "auto-bind": "^5.0.1", "chalk": "^5.2.0", "cli-boxes": "^3.0.0", "cli-cursor": "^4.0.0", "cli-truncate": "^3.1.0", "code-excerpt": "^4.0.0", "indent-string": "^5.0.0", "is-ci": "^3.0.1", "is-lower-case": "^2.0.2", "is-upper-case": "^2.0.2", "lodash": "^4.17.21", "patch-console": "^2.0.0", "react-reconciler": "^0.29.0", "scheduler": "^0.23.0", "signal-exit": "^3.0.7", "slice-ansi": "^6.0.0", "stack-utils": "^2.0.6", "string-width": "^5.1.2", "type-fest": "^0.12.0", "widest-line": "^4.0.1", "wrap-ansi": "^8.1.0", "ws": "^8.12.0", "yoga-wasm-web": "~0.3.3"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@sindresorhus/tsconfig": "3.0.1", "@types/benchmark": "^2.1.2", "@types/is-ci": "^2.0.0", "@types/lodash": "^4.14.191", "@types/ms": "^0.7.31", "@types/node": "*", "@types/react": "^18.0.0", "@types/react-reconciler": "^0.28.2", "@types/scheduler": "^0.16.2", "@types/signal-exit": "^3.0.0", "@types/sinon": "^9.0.4", "@types/slice-ansi": "^4.0.0", "@types/stack-utils": "^1.0.1", "@types/wrap-ansi": "^3.0.0", "@types/ws": "^8.5.4", "@vdemedes/prettier-config": "^1.0.1", "ava": "^5.1.1", "boxen": "^7.0.1", "delay": "^5.0.0", "eslint-config-xo-react": "0.27.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "ms": "^2.1.3", "node-pty": "0.10.1", "p-queue": "^7.3.4", "prettier": "^2.0.4", "react": "^18.0.0", "react-devtools-core": "^4.19.1", "sinon": "^12.0.1", "strip-ansi": "^6.0.0", "ts-node": "10.9.1", "typescript": "^4.9.4", "xo": "0.54.2"}, "peerDependencies": {"@types/react": ">=18.0.0", "react": ">=18.0.0", "react-devtools-core": "^4.19.1"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "react-devtools-core": {"optional": true}}, "ava": {"workerThreads": false, "files": ["test/**/*", "!test/helpers/**/*", "!test/fixtures/**/*"], "extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "xo": {"extends": ["xo-react"], "plugins": ["react"], "prettier": true, "rules": {"react/no-unescaped-entities": "off", "react/state-in-constructor": "off", "react/jsx-indent": "off", "react/prop-types": "off", "unicorn/import-index": "off", "import/no-useless-path-segments": "off", "react-hooks/exhaustive-deps": "off", "complexity": "off"}, "ignores": ["src/parse-keypress.ts"], "overrides": [{"files": ["src/**/*.{ts,tsx}", "test/**/*.{ts,tsx}"], "rules": {"no-unused-expressions": "off", "camelcase": ["error", {"allow": ["^unstable__", "^internal_"]}], "unicorn/filename-case": "off", "react/default-props-match-prop-types": "off", "unicorn/prevent-abbreviations": "off", "react/require-default-props": "off", "react/jsx-curly-brace-presence": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/promise-function-async": "warn", "@typescript-eslint/explicit-function-return": "off", "@typescript-eslint/explicit-function-return-type": "off", "dot-notation": "off", "react/boolean-prop-naming": "off", "unicorn/prefer-dom-node-remove": "off", "unicorn/prefer-event-target": "off"}}, {"files": ["examples/**/*.{ts,tsx}", "benchmark/**/*.{ts,tsx}"], "rules": {"import/no-unassigned-import": "off"}}]}, "prettier": "@vdemedes/prettier-config"}