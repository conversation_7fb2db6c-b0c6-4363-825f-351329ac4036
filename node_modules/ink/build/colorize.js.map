{"version": 3, "file": "colorize.js", "sourceRoot": "", "sources": ["../src/colorize.ts"], "names": [], "mappings": "AAAA,OAAO,KAA2D,MAAM,OAAO,CAAC;AAIhF,MAAM,QAAQ,GAAG,wCAAwC,CAAC;AAC1D,MAAM,SAAS,GAAG,0BAA0B,CAAC;AAE7C,MAAM,YAAY,GAAG,CAAC,KAAa,EAAgC,EAAE;IACpE,OAAO,KAAK,IAAI,KAAK,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,QAAQ,GAAG,CAChB,GAAW,EACX,KAAyB,EACzB,IAAe,EACN,EAAE;IACX,IAAI,CAAC,KAAK,EAAE;QACX,OAAO,GAAG,CAAC;KACX;IAED,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;QACxB,IAAI,IAAI,KAAK,YAAY,EAAE;YAC1B,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;SACzB;QAED,MAAM,UAAU,GAAG,KAClB,KAAK,CAAC,CAAC,CAAE,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CACxC,EAAyB,CAAC;QAE1B,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;KAC9B;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC1B,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YACvB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;QAChC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,EAAE;YACb,OAAO,GAAG,CAAC;SACX;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YAC3B,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;KAC/B;IAED,IAAI,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAErC,IAAI,CAAC,OAAO,EAAE;YACb,OAAO,GAAG,CAAC;SACX;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,OAAO,IAAI,KAAK,YAAY;YAC3B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC;YACrD,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;KACzD;IAED,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AAEF,eAAe,QAAQ,CAAC"}