{"version": 3, "file": "reconciler.js", "sourceRoot": "", "sources": ["../src/reconciler.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,gBAAgB,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAC,oBAAoB,EAAC,MAAM,+BAA+B,CAAC;AACnE,sDAAsD;AACtD,OAAO,IAA6B,MAAM,oBAAoB,CAAC;AAC/D,OAAO,EACN,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,YAAY,EAKZ,MAAM,UAAU,CAAC;AAClB,OAAO,WAA0B,MAAM,aAAa,CAAC;AAGrD,gEAAgE;AAChE,gDAAgD;AAChD,qDAAqD;AACrD,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;IAClC,IAAI;QACH,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;KAC9B;IAAC,OAAO,KAAU,EAAE;QACpB,IAAI,KAAK,CAAC,IAAI,KAAK,kBAAkB,EAAE;YACtC,OAAO,CAAC,IAAI,CACX;;;;KAIC,CAAC,IAAI,EAAE,GAAG,IAAI,CACf,CAAC;SACF;aAAM;YACN,+DAA+D;YAC/D,MAAM,KAAK,CAAC;SACZ;KACD;CACD;AAID,MAAM,IAAI,GAAG,CAAC,MAAiB,EAAE,KAAgB,EAAyB,EAAE;IAC3E,IAAI,MAAM,KAAK,KAAK,EAAE;QACrB,OAAO;KACP;IAED,IAAI,CAAC,MAAM,EAAE;QACZ,OAAO,KAAK,CAAC;KACb;IAED,MAAM,OAAO,GAAc,EAAE,CAAC;IAC9B,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACtC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzE,IAAI,SAAS,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;YACzB,SAAS,GAAG,IAAI,CAAC;SACjB;KACD;IAED,IAAI,KAAK,EAAE;QACV,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACrC,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,EAAE;gBAC/B,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1B,SAAS,GAAG,IAAI,CAAC;aACjB;SACD;KACD;IAED,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;AACxC,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,IAAe,EAAQ,EAAE;IACjD,IAAI,EAAE,gBAAgB,EAAE,CAAC;IACzB,IAAI,EAAE,aAAa,EAAE,CAAC;AACvB,CAAC,CAAC;AAaF,eAAe,gBAAgB,CAc7B;IACD,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1B,YAAY,EAAE,KAAK;KACnB,CAAC;IACF,gBAAgB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC5B,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC9B,cAAc,EAAE,GAAG,EAAE,CAAC,KAAK;IAC3B,gBAAgB,CAAC,QAAQ;QACxB,IAAI,OAAO,QAAQ,CAAC,eAAe,KAAK,UAAU,EAAE;YACnD,QAAQ,CAAC,eAAe,EAAE,CAAC;SAC3B;QAED,oFAAoF;QACpF,0EAA0E;QAC1E,uGAAuG;QACvG,IAAI,QAAQ,CAAC,aAAa,EAAE;YAC3B,QAAQ,CAAC,aAAa,GAAG,KAAK,CAAC;YAC/B,IAAI,OAAO,QAAQ,CAAC,iBAAiB,KAAK,UAAU,EAAE;gBACrD,QAAQ,CAAC,iBAAiB,EAAE,CAAC;aAC7B;YAED,OAAO;SACP;QAED,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU,EAAE;YAC5C,QAAQ,CAAC,QAAQ,EAAE,CAAC;SACpB;IACF,CAAC;IACD,mBAAmB,CAAC,iBAAiB,EAAE,IAAI;QAC1C,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,YAAY,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,kBAAkB,CAAC;QAExE,IAAI,oBAAoB,KAAK,YAAY,EAAE;YAC1C,OAAO,iBAAiB,CAAC;SACzB;QAED,OAAO,EAAC,YAAY,EAAC,CAAC;IACvB,CAAC;IACD,oBAAoB,EAAE,GAAG,EAAE,CAAC,KAAK;IACjC,cAAc,CAAC,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW;QACxD,IAAI,WAAW,CAAC,YAAY,IAAI,YAAY,KAAK,SAAS,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SACjE;QAED,MAAM,IAAI,GACT,YAAY,KAAK,UAAU,IAAI,WAAW,CAAC,YAAY;YACtD,CAAC,CAAC,kBAAkB;YACpB,CAAC,CAAC,YAAY,CAAC;QAEjB,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAE9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YACpD,IAAI,GAAG,KAAK,UAAU,EAAE;gBACvB,SAAS;aACT;YAED,IAAI,GAAG,KAAK,OAAO,EAAE;gBACpB,QAAQ,CAAC,IAAI,EAAE,KAAe,CAAC,CAAC;gBAEhC,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAClB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAe,CAAC,CAAC;iBAC5C;gBAED,SAAS;aACT;YAED,IAAI,GAAG,KAAK,oBAAoB,EAAE;gBACjC,IAAI,CAAC,kBAAkB,GAAG,KAA0B,CAAC;gBACrD,SAAS;aACT;YAED,IAAI,GAAG,KAAK,iBAAiB,EAAE;gBAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,SAAS;aACT;YAED,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAyB,CAAC,CAAC;SACnD;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW;QAC1C,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE;YAC9B,MAAM,IAAI,KAAK,CACd,gBAAgB,IAAI,4CAA4C,CAChE,CAAC;SACF;QAED,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IACD,gBAAgB,KAAI,CAAC;IACrB,gBAAgB,CAAC,IAAI;QACpB,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC5B,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,IAAI;QAC5B,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACD,iBAAiB,EAAE,QAAQ,CAAC,EAAE,CAAC,QAAQ;IACvC,YAAY,CAAC,IAAI;QAChB,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IACD,cAAc,CAAC,IAAI;QAClB,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9C,CAAC;IACD,kBAAkB,EAAE,eAAe;IACnC,WAAW,EAAE,eAAe;IAC5B,YAAY,EAAE,gBAAgB;IAC9B,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ;QACpD,IAAI,IAAI,CAAC,eAAe,EAAE;YACzB,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;YAE9B,8DAA8D;YAC9D,uBAAuB;YACvB,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC3B;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IACD,iBAAiB,EAAE,IAAI;IACvB,gBAAgB,EAAE,IAAI;IACtB,mBAAmB,EAAE,KAAK;IAC1B,iBAAiB,EAAE,KAAK;IACxB,eAAe,EAAE,UAAU;IAC3B,aAAa,EAAE,YAAY;IAC3B,SAAS,EAAE,CAAC,CAAC;IACb,uBAAuB,EAAE,GAAG,EAAE,CAAC,oBAAoB;IACnD,wBAAwB,KAAI,CAAC;IAC7B,uBAAuB,KAAI,CAAC;IAC5B,qBAAqB,KAAI,CAAC;IAC1B,mBAAmB,EAAE,GAAG,EAAE,CAAC,IAAI;IAC/B,kBAAkB,KAAI,CAAC;IACvB,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI;IAChC,sBAAsB,EAAE,eAAe;IACvC,uBAAuB,EAAE,gBAAgB;IACzC,wBAAwB,CAAC,IAAI,EAAE,UAAU;QACxC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IACD,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ;QACtD,IAAI,IAAI,CAAC,eAAe,EAAE;YACzB,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC;SAC9B;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEvC,MAAM,KAAK,GAAG,IAAI,CACjB,QAAQ,CAAC,OAAO,CAAW,EAC3B,QAAQ,CAAC,OAAO,CAAW,CAC3B,CAAC;QAEF,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;YACrB,OAAO,IAAI,CAAC;SACZ;QAED,OAAO,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC;IACvB,CAAC;IACD,YAAY,CAAC,IAAI,EAAE,EAAC,KAAK,EAAE,KAAK,EAAC;QAChC,IAAI,KAAK,EAAE;YACV,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACjD,IAAI,GAAG,KAAK,OAAO,EAAE;oBACpB,QAAQ,CAAC,IAAI,EAAE,KAAe,CAAC,CAAC;oBAChC,SAAS;iBACT;gBAED,IAAI,GAAG,KAAK,oBAAoB,EAAE;oBACjC,IAAI,CAAC,kBAAkB,GAAG,KAA0B,CAAC;oBACrD,SAAS;iBACT;gBAED,IAAI,GAAG,KAAK,iBAAiB,EAAE;oBAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,SAAS;iBACT;gBAED,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE,KAAyB,CAAC,CAAC;aACnD;SACD;QAED,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;YAC3B,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SAClC;IACF,CAAC;IACD,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO;QACvC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACjC,CAAC;IACD,WAAW,CAAC,IAAI,EAAE,UAAU;QAC3B,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAClC,eAAe,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;CACD,CAAC,CAAC"}