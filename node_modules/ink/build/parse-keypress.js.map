{"version": 3, "file": "parse-keypress.js", "sourceRoot": "", "sources": ["../src/parse-keypress.ts"], "names": [], "mappings": "AAAA,iHAAiH;AACjH,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AAEnC,MAAM,aAAa,GAAG,yBAAyB,CAAC;AAEhD,MAAM,OAAO,GACZ,4EAA4E,CAAC;AAE9E,MAAM,OAAO,GAA2B;IACvC,8BAA8B;IAC9B,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,IAAI;IACR,+BAA+B;IAC/B,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,mCAAmC;IACnC,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,YAAY;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,wBAAwB;IACxB,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,MAAM;IACZ,8BAA8B;IAC9B,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,KAAK;IACT,EAAE,EAAE,MAAM;IACV,+BAA+B;IAC/B,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,UAAU;IACjB,WAAW;IACX,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,UAAU;IAClB,UAAU;IACV,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK;IACZ,8BAA8B;IAC9B,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,OAAO;IAEb,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK;IAEZ,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,OAAO;IAEX,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,KAAK;IACZ,WAAW;IACX,IAAI,EAAE,KAAK;CACX,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC;AAE5E,MAAM,UAAU,GAAG,CAAC,IAAY,EAAE,EAAE;IACnC,OAAO;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,IAAI;KACJ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,EAAE;IAClC,OAAO;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;KACL,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC;AAaF,MAAM,aAAa,GAAG,CAAC,IAAqB,EAAE,EAAa,EAAE;IAC5D,IAAI,KAAK,CAAC;IAEV,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,CAAC,CAAC,CAAC,CAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YACrC,CAAC,CAAC,CAAC,CAAuB,IAAI,GAAG,CAAC;YACnC,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACvB;aAAM;YACN,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACd;KACD;SAAM,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QACpD,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACd;SAAM,IAAI,CAAC,CAAC,EAAE;QACd,CAAC,GAAG,EAAE,CAAC;KACP;IAED,MAAM,GAAG,GAAc;QACtB,IAAI,EAAE,EAAE;QACR,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,KAAK;QACX,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,CAAC;QACX,GAAG,EAAE,CAAC;KACN,CAAC;IAEF,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;IAE7C,IAAI,CAAC,KAAK,IAAI,EAAE;QACf,kBAAkB;QAClB,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC;QACpB,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;KACpB;SAAM,IAAI,CAAC,KAAK,IAAI,EAAE;QACtB,0CAA0C;QAC1C,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC;KACnB;SAAM,IAAI,CAAC,KAAK,IAAI,EAAE;QACtB,MAAM;QACN,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC;KACjB;SAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,QAAQ,EAAE;QACxC,sBAAsB;QACtB,GAAG,CAAC,IAAI,GAAG,WAAW,CAAC;QACvB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;KAClC;SAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE;QAC5C,mLAAmL;QACnL,SAAS;QACT,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;QACpB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;KAClC;SAAM,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,UAAU,EAAE;QAC5C,aAAa;QACb,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;QACpB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;KAC1B;SAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,OAAO,EAAE;QACtC,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC;QACnB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;KAC1B;SAAM,IAAI,CAAC,IAAI,MAAM,EAAE;QACvB,cAAc;QACd,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxE,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;KAChB;SAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE;QAClD,SAAS;QACT,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC;KACpB;SAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE;QAClD,mBAAmB;QACnB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;KACb;SAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE;QAClD,eAAe;QACf,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;QAC3B,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC;KACjB;SAAM,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3C,qBAAqB;QACrB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC;KACtC;SAAM,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACrC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YACjD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;SAClB;QAED,uBAAuB;QACvB,sDAAsD;QACtD,6DAA6D;QAC7D,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aACnD,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,EAAE,CAAC,CAAC;QAEX,MAAM,QAAQ,GAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAY,GAAG,CAAC,CAAC;QAE7D,yBAAyB;QACzB,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC5B,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC;QAC7B,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC7B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAEhB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAE,CAAC;QAC1B,GAAG,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC;QAC1C,GAAG,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;KACvC;IAED,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AAEF,eAAe,aAAa,CAAC"}