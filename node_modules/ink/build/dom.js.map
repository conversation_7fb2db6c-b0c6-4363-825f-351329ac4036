{"version": 3, "file": "dom.js", "sourceRoot": "", "sources": ["../src/dom.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,IAA6B,MAAM,oBAAoB,CAAC;AAC/D,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAE5C,OAAO,QAAQ,MAAM,gBAAgB,CAAC;AACtC,OAAO,eAAe,MAAM,wBAAwB,CAAC;AAmDrD,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,QAAsB,EAAc,EAAE;IAChE,MAAM,IAAI,GAAe;QACxB,QAAQ;QACR,KAAK,EAAE,EAAE;QACT,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,EAAE;QACd,UAAU,EAAE,SAAS;QACrB,QAAQ,EAAE,QAAQ,KAAK,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;KAC1E,CAAC;IAEF,IAAI,QAAQ,KAAK,UAAU,EAAE;QAC5B,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;KAChE;IAED,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAC9B,IAAgB,EAChB,SAAqB,EACd,EAAE;IACT,IAAI,SAAS,CAAC,UAAU,EAAE;QACzB,eAAe,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;KACjD;IAED,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEhC,IAAI,SAAS,CAAC,QAAQ,EAAE;QACvB,IAAI,CAAC,QAAQ,EAAE,WAAW,CACzB,SAAS,CAAC,QAAQ,EAClB,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAC7B,CAAC;KACF;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,EAAE;QACzE,eAAe,CAAC,IAAI,CAAC,CAAC;KACtB;AACF,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC/B,IAAgB,EAChB,YAAqB,EACrB,eAAwB,EACjB,EAAE;IACT,IAAI,YAAY,CAAC,UAAU,EAAE;QAC5B,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;KACvD;IAED,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;IAE/B,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACvD,IAAI,KAAK,IAAI,CAAC,EAAE;QACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAC/C,IAAI,YAAY,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACzD;QAED,OAAO;KACP;IAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAEnC,IAAI,YAAY,CAAC,QAAQ,EAAE;QAC1B,IAAI,CAAC,QAAQ,EAAE,WAAW,CACzB,YAAY,CAAC,QAAQ,EACrB,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAC7B,CAAC;KACF;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,EAAE;QACzE,eAAe,CAAC,IAAI,CAAC,CAAC;KACtB;AACF,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG,CAC9B,IAAgB,EAChB,UAAmB,EACZ,EAAE;IACT,IAAI,UAAU,CAAC,QAAQ,EAAE;QACxB,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KAClE;IAED,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;IAElC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAClD,IAAI,KAAK,IAAI,CAAC,EAAE;QACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACjC;IAED,IAAI,IAAI,CAAC,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,EAAE;QACzE,eAAe,CAAC,IAAI,CAAC,CAAC;KACtB;AACF,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAC3B,IAAgB,EAChB,GAAW,EACX,KAAuB,EAChB,EAAE;IACT,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAC9B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,IAAa,EAAE,KAAa,EAAQ,EAAE;IAC9D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,IAAY,EAAY,EAAE;IACxD,MAAM,IAAI,GAAa;QACtB,QAAQ,EAAE,OAAO;QACjB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,SAAS;QACnB,UAAU,EAAE,SAAS;QACrB,KAAK,EAAE,EAAE;KACT,CAAC;IAEF,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAE7B,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,UACvB,IAAa,EACb,KAAa;IAEb,MAAM,IAAI,GACT,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAEpE,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,4CAA4C;IAC5C,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;QAC9B,OAAO,UAAU,CAAC;KAClB;IAED,sEAAsE;IACtE,0EAA0E;IAC1E,IAAI,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;QACpD,OAAO,UAAU,CAAC;KAClB;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,IAAI,MAAM,CAAC;IAChD,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAEpD,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,IAAc,EAAwB,EAAE;IACpE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE;QACtB,OAAO,SAAS,CAAC;KACjB;IAED,OAAO,IAAI,CAAC,QAAQ,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,IAAc,EAAQ,EAAE;IAChD,mEAAmE;IACnE,MAAM,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC3C,QAAQ,EAAE,SAAS,EAAE,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAc,EAAE,IAAY,EAAQ,EAAE;IACtE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC7B,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;IAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,eAAe,CAAC,IAAI,CAAC,CAAC;AACvB,CAAC,CAAC"}