{"version": 3, "file": "ink.js", "sourceRoot": "", "sources": ["../src/ink.tsx"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,cAAc,CAAC;AACnC,OAAO,KAAuB,MAAM,OAAO,CAAC;AAC5C,OAAO,QAAQ,MAAM,oBAAoB,CAAC;AAE1C,OAAO,WAAW,MAAM,cAAc,CAAC;AACvC,OAAO,YAAY,MAAM,OAAO,CAAC;AACjC,OAAO,QAAQ,MAAM,WAAW,CAAC;AACjC,OAAO,UAAU,MAAM,aAAa,CAAC;AACrC,OAAO,YAAY,MAAM,eAAe,CAAC;AAEzC,sDAAsD;AACtD,OAAO,IAAI,MAAM,oBAAoB,CAAC;AACtC,OAAO,UAAU,MAAM,iBAAiB,CAAC;AACzC,OAAO,MAAM,MAAM,eAAe,CAAC;AACnC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAC;AAChC,OAAO,SAA2B,MAAM,iBAAiB,CAAC;AAC1D,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,GAAG,MAAM,qBAAqB,CAAC;AAEtC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;AAClE,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;AAYtB,MAAM,CAAC,OAAO,OAAO,GAAG;IAgBvB,YAAY,OAAgB;QAf5B;;;;;WAAkC;QAClC;;;;;WAAgC;QAChC;;;;;WAAoE;QACpE,iFAAiF;QACjF;;;;;WAA6B;QAC7B;;;;;WAA2B;QAC3B;;;;;WAAsC;QACtC;;;;;WAA0C;QAC1C,uEAAuE;QACvE,wFAAwF;QACxF;;;;;WAAiC;QACjC;;;;;WAAoC;QACpC;;;;;WAAoC;QACpC;;;;;WAAgD;QA0EhD;;;;mBAAU,GAAG,EAAE;gBACd,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,CAAC;WAAC;QAEF;;;;mBAAiC,GAAG,EAAE,GAAE,CAAC;WAAC;QAC1C;;;;mBAA8C,GAAG,EAAE,GAAE,CAAC;WAAC;QACvD;;;;mBAA8B,GAAG,EAAE,GAAE,CAAC;WAAC;QAEvC;;;;mBAAkB,GAAG,EAAE;gBACtB,qEAAqE;gBACrE,mCAAmC;gBACnC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;gBAExD,IAAI,CAAC,QAAQ,CAAC,QAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAEhD,IAAI,CAAC,QAAQ,CAAC,QAAS,CAAC,eAAe,CACtC,SAAS,EACT,SAAS,EACT,IAAI,CAAC,aAAa,CAClB,CAAC;YACH,CAAC;WAAC;QAEF;;;;mBAAuB,GAAG,EAAE;gBAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;oBACrB,OAAO;iBACP;gBAED,MAAM,EAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAC,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEnE,8EAA8E;gBAC9E,MAAM,eAAe,GAAG,YAAY,IAAI,YAAY,KAAK,IAAI,CAAC;gBAE9D,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;oBACvB,IAAI,eAAe,EAAE;wBACpB,IAAI,CAAC,gBAAgB,IAAI,YAAY,CAAC;qBACtC;oBAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,CAAC;oBAC1D,OAAO;iBACP;gBAED,IAAI,IAAI,EAAE;oBACT,IAAI,eAAe,EAAE;wBACpB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;qBACxC;oBAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;oBACzB,OAAO;iBACP;gBAED,IAAI,eAAe,EAAE;oBACpB,IAAI,CAAC,gBAAgB,IAAI,YAAY,CAAC;iBACtC;gBAED,IAAI,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CACxB,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAC1D,CAAC;oBACF,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;oBACzB,OAAO;iBACP;gBAED,0FAA0F;gBAC1F,IAAI,eAAe,EAAE;oBACpB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;oBACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBACxC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBACjB;gBAED,IAAI,CAAC,eAAe,IAAI,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE;oBACnD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;iBAC1B;gBAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YAC1B,CAAC;WAAC;QAlJD,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAErD,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK;YACrC,CAAC,CAAC,IAAI,CAAC,QAAQ;YACf,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;gBAC5B,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACb,CAAC,CAAC;QAEN,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK;YAChC,CAAC,CAAC,IAAI,CAAC,GAAG;YACV,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE;gBAC9B,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE,IAAI;aACb,CAAC,CAAC;QAEN,iFAAiF;QACjF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,iDAAiD;QACjD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QAErB,uEAAuE;QACvE,wFAAwF;QACxF,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAE3B,mEAAmE;QACnE,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAC1C,IAAI,CAAC,QAAQ;QACb,cAAc;QACd,CAAC,EACD,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,GAAG,EAAE,GAAE,CAAC,EACR,IAAI,CACJ,CAAC;QAEF,6BAA6B;QAC7B,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,UAAU,EAAE,KAAK,EAAC,CAAC,CAAC;QAErE,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;YAClC,UAAU,CAAC,kBAAkB,CAAC;gBAC7B,UAAU,EAAE,CAAC;gBACb,2CAA2C;gBAC3C,4EAA4E;gBAC5E,OAAO,EAAE,SAAS;gBAClB,mBAAmB,EAAE,KAAK;aAC1B,CAAC,CAAC;SACH;QAED,IAAI,OAAO,CAAC,YAAY,EAAE;YACzB,IAAI,CAAC,YAAY,EAAE,CAAC;SACpB;QAED,IAAI,CAAC,IAAI,EAAE;YACV,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAE1C,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;gBAC7B,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC,CAAC;SACF;IACF,CAAC;IA+ED,MAAM,CAAC,IAAe;QACrB,MAAM,IAAI,GAAG,CACZ,oBAAC,GAAG,IACH,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EACzB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAC3B,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAC3B,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,aAAa,EAAE,IAAI,CAAC,aAAa,EACjC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EACrC,MAAM,EAAE,IAAI,CAAC,OAAO,IAEnB,IAAI,CACA,CACN,CAAC;QAEF,UAAU,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED,aAAa,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACP;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1E,OAAO;SACP;QAED,IAAI,IAAI,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;SACP;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC;IAED,aAAa,CAAC,IAAY;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACP;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;YACnE,OAAO;SACP;QAED,IAAI,IAAI,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO;SACP;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC3B,CAAC;IAED,wDAAwD;IACxD,OAAO,CAAC,KAA6B;QACpC,IAAI,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO;SACP;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,OAAO,IAAI,CAAC,cAAc,KAAK,UAAU,EAAE;YAC9C,IAAI,CAAC,cAAc,EAAE,CAAC;SACtB;QAED,IAAI,OAAO,IAAI,CAAC,iBAAiB,KAAK,UAAU,EAAE;YACjD,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACzB;QAED,gEAAgE;QAChE,8CAA8C;QAC9C,IAAI,IAAI,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;SAClD;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,UAAU,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEtC,IAAI,KAAK,YAAY,KAAK,EAAE;YAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM;YACN,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC1B;IACF,CAAC;IAED,KAAK,CAAC,aAAa;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACtB,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAClD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;gBAClC,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC;YACjC,CAAC,CAAC,CAAC;SACH;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;IACzB,CAAC;IAED,KAAK;QACJ,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACjC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;SACjB;IACF,CAAC;IAED,YAAY;QACX,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACvB,OAAO;SACP;QAED,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YACnD,IAAI,MAAM,KAAK,QAAQ,EAAE;gBACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aACzB;YAED,IAAI,MAAM,KAAK,QAAQ,EAAE;gBACxB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;gBAEnE,IAAI,CAAC,cAAc,EAAE;oBACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBACzB;aACD;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD"}