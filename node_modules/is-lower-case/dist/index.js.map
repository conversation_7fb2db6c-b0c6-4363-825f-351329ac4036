{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,SAAgB,WAAW,CAAC,KAAa;IACvC,OAAO,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;AACxE,CAAC;AAFD,kCAEC", "sourcesContent": ["/**\n * Returns a boolean indicating whether the string is lower case.\n */\nexport function isLowerCase(input: string) {\n  return input.toLowerCase() === input && input.toUpperCase() !== input;\n}\n"]}