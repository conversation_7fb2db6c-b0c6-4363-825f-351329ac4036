{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,sBAAgC;AAEhC,IAAM,UAAU,GAAwB;IACtC,CAAC,EAAE,EAAE,KAAK,CAAC;IACX,CAAC,MAAM,EAAE,IAAI,CAAC;IACd,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,YAAY,EAAE,IAAI,CAAC;CACrB,CAAC;AAEF,QAAQ,CAAC,eAAe,EAAE;4BACZ,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,cAAW,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { isLowerCase } from \".\";\n\nconst TEST_CASES: [string, boolean][] = [\n  [\"\", false],\n  [\"test\", true],\n  [\"TEST\", false],\n  [\"Test\", false],\n  [\"123\", false],\n  [\"snake_case\", true],\n];\n\ndescribe(\"is lower case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(isLowerCase(input)).toEqual(result);\n    });\n  }\n});\n"]}