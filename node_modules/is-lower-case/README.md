# Is Lower Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Returns `true` if the string is lower case only.

## Installation

```
npm install is-lower-case --save
```

## Usage

```js
import { isLowerCase } from "is-lower-case";

isLowerCase("string"); //=> true
isLowerCase("dot.case"); //=> true
isLowerCase("PascalCase"); //=> false
isLowerCase("version 1.2.10"); //=> true
```

## License

MIT

[npm-image]: https://img.shields.io/npm/v/is-lower-case.svg?style=flat
[npm-url]: https://npmjs.org/package/is-lower-case
[downloads-image]: https://img.shields.io/npm/dm/is-lower-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/is-lower-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/is-lower-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=is-lower-case
