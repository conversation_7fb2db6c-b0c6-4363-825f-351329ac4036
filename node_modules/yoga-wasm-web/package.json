{"name": "yoga-wasm-web", "version": "0.3.3", "type": "module", "types": "dist/index.d.ts", "typesVersions": {"*": {"asm": ["dist/asm.d.ts"], "auto": ["dist/auto.d.ts"]}}, "exports": {"./package.json": "./package.json", "./dist/yoga.wasm": "./dist/yoga.wasm", ".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./asm": {"types": "./dist/asm.d.ts", "default": "./dist/asm.js"}, "./auto": {"types": "./dist/auto.d.ts", "browser": "./dist/browser.js", "default": "./dist/node.js"}}, "repository": "shuding/yoga-wasm-web", "license": "MIT", "files": ["dist", "package.json", "LICENSE"], "devDependencies": {"@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@swc/core": "^1.3.36", "rollup": "^3.17.0", "rollup-plugin-swc3": "^0.8.0", "typescript": "^4.9.5", "vitest": "^0.28.5"}, "scripts": {"build": "make && rollup -c", "typecheck": "tsc --noEmit", "test": "pnpm test:asm && pnpm test:wasm", "test:asm": "ASM=true vitest run --dir ./test", "test:wasm": "vitest run --dir ./test"}}