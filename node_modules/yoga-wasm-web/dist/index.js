import{w as n}from"./wrapAsm-f766f97f.js";export{A as ALIGN_AUTO,e as ALIGN_BASELINE,b as ALIGN_CENTER,c as ALIGN_FLEX_END,a as ALIGN_FLEX_START,g as ALIG<PERSON>_SPACE_AROUND,f as ALIGN_SPACE_BETWEEN,d as ALIGN_STRETCH,h as DIMENSION_HEIGHT,D as DIMENSION_WIDTH,i as DIRECTION_INHERIT,j as DIRECTION_LTR,k as DIRECTION_RTL,l as DISPLAY_FLEX,m as DISPLAY_NONE,u as EDGE_ALL,p as EDGE_BOTTOM,r as EDGE_END,s as EDGE_HORIZONTAL,E as EDGE_LEFT,o as EDGE_RIGHT,q as EDGE_START,n as EDGE_TOP,t as EDGE_VERTICAL,x as EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE,y as EX<PERSON><PERSON>IMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN,v as EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS,F as FLEX_DIRECTION_COLUMN,z as FLEX_DIRECTION_COLUMN_REVERSE,B as FLEX_DIRECTION_ROW,C as FLEX_DIRECTION_ROW_REVERSE,I as GUTTER_ALL,G as GUTTER_COLUMN,H as GUTTER_ROW,K as JUSTIFY_CENTER,L as JUSTIFY_FLEX_END,J as JUSTIFY_FLEX_START,N as JUSTIFY_SPACE_AROUND,M as JUSTIFY_SPACE_BETWEEN,O as JUSTIFY_SPACE_EVENLY,S as LOG_LEVEL_DEBUG,P as LOG_LEVEL_ERROR,U as LOG_LEVEL_FATAL,R as LOG_LEVEL_INFO,T as LOG_LEVEL_VERBOSE,Q as LOG_LEVEL_WARN,X as MEASURE_MODE_AT_MOST,W as MEASURE_MODE_EXACTLY,V as MEASURE_MODE_UNDEFINED,Y as NODE_TYPE_DEFAULT,Z as NODE_TYPE_TEXT,$ as OVERFLOW_HIDDEN,a0 as OVERFLOW_SCROLL,_ as OVERFLOW_VISIBLE,a3 as POSITION_TYPE_ABSOLUTE,a2 as POSITION_TYPE_RELATIVE,a1 as POSITION_TYPE_STATIC,a6 as PRINT_OPTIONS_CHILDREN,a4 as PRINT_OPTIONS_LAYOUT,a5 as PRINT_OPTIONS_STYLE,aa as UNIT_AUTO,a9 as UNIT_PERCENT,a8 as UNIT_POINT,a7 as UNIT_UNDEFINED,ab as WRAP_NO_WRAP,ac as WRAP_WRAP,ad as WRAP_WRAP_REVERSE}from"./wrapAsm-f766f97f.js";var yoga=(()=>{var n="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(t={}){u||(u=void 0!==t?t:{}),u.ready=new Promise(function(n,t){c=n,f=t});var r,e,a=Object.assign({},u),i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src),n&&(i=n),i=0!==i.indexOf("blob:")?i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var o=console.log.bind(console),s=console.warn.bind(console);Object.assign(u,a),a=null,"object"!=typeof WebAssembly&&w("no native wasm support detected");var u,c,f,l,h=!1;function p(n,t,r){r=t+r;for(var e="";!(t>=r);){var a=n[t++];if(!a)break;if(128&a){var i=63&n[t++];if(192==(224&a))e+=String.fromCharCode((31&a)<<6|i);else{var o=63&n[t++];65536>(a=224==(240&a)?(15&a)<<12|i<<6|o:(7&a)<<18|i<<12|o<<6|63&n[t++])?e+=String.fromCharCode(a):(a-=65536,e+=String.fromCharCode(55296|a>>10,56320|1023&a))}}else e+=String.fromCharCode(a)}return e}function v(){var n=l.buffer;u.HEAP8=d=new Int8Array(n),u.HEAP16=m=new Int16Array(n),u.HEAP32=g=new Int32Array(n),u.HEAPU8=y=new Uint8Array(n),u.HEAPU16=E=new Uint16Array(n),u.HEAPU32=_=new Uint32Array(n),u.HEAPF32=T=new Float32Array(n),u.HEAPF64=L=new Float64Array(n)}var d,y,m,E,g,_,T,L,A,O=[],P=[],b=[],N=0,I=null;function w(n){throw s(n="Aborted("+n+")"),h=!0,f(n=new WebAssembly.RuntimeError(n+". Build with -sASSERTIONS for more info.")),n}function S(){return r.startsWith("data:application/octet-stream;base64,")}function R(){try{throw"both async and sync fetching of the wasm failed"}catch(n){w(n)}}function C(n){for(;0<n.length;)n.shift()(u)}function W(n){if(void 0===n)return"_unknown";var t=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&57>=t?"_"+n:n}function U(n,t){return n=W(n),function(){return t.apply(this,arguments)}}r="yoga.wasm",S()||(r=i+r);var M=[{},{value:void 0},{value:null},{value:!0},{value:!1}],F=[];function D(n){var t=Error,r=U(n,function(t){this.name=n,this.message=t,void 0!==(t=Error(t).stack)&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))});return r.prototype=Object.create(t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var k=void 0;function V(n){throw new k(n)}var j=n=>(n||V("Cannot use deleted val. handle = "+n),M[n].value),G=n=>{switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=F.length?F.pop():M.length;return M[t]={fa:1,value:n},t}},Y=void 0,X=void 0;function B(n){for(var t="";y[n];)t+=X[y[n++]];return t}var H=[];function x(){for(;H.length;){var n=H.pop();n.L.Z=!1,n.delete()}}var z=void 0,$={};function Z(n,t){for(void 0===t&&V("ptr should not be undefined");n.P;)t=n.aa(t),n=n.P;return t}var J={};function q(n){var t=B(n=nz(n));return nZ(n),t}function K(n,t){var r=J[n];return void 0===r&&V(t+" has unknown type "+q(n)),r}function Q(){}var nn=!1;function nt(n){--n.count.value,0===n.count.value&&(n.S?n.T.V(n.S):n.O.M.V(n.N))}var nr={},ne=void 0;function na(n){throw new ne(n)}function ni(n,t){return t.O&&t.N||na("makeClassHandle requires ptr and ptrType"),!!t.T!=!!t.S&&na("Both smartPtrType and smartPtr must be specified"),t.count={value:1},no(Object.create(n,{L:{value:t}}))}function no(n){return"undefined"==typeof FinalizationRegistry?(no=n=>n,n):(nn=new FinalizationRegistry(n=>{nt(n.L)}),no=n=>{var t=n.L;return t.S&&nn.register(n,{L:t},n),n},Q=n=>{nn.unregister(n)},no(n))}var ns={};function nu(n){for(;n.length;){var t=n.pop();n.pop()(t)}}function nc(n){return this.fromWireType(g[n>>2])}var nf={},nl={};function nh(n,t,r){function e(t){(t=r(t)).length!==n.length&&na("Mismatched type converter count");for(var e=0;e<n.length;++e)nv(n[e],t[e])}n.forEach(function(n){nl[n]=t});var a=Array(t.length),i=[],o=0;t.forEach((n,t)=>{J.hasOwnProperty(n)?a[t]=J[n]:(i.push(n),nf.hasOwnProperty(n)||(nf[n]=[]),nf[n].push(()=>{a[t]=J[n],++o===i.length&&e(a)}))}),0===i.length&&e(a)}function np(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw TypeError("Unknown type size: "+n)}}function nv(n,t,r={}){if(!("argPackAdvance"in t))throw TypeError("registerType registeredInstance requires argPackAdvance");var e=t.name;if(n||V('type "'+e+'" must have a positive integer typeid pointer'),J.hasOwnProperty(n)){if(r.ta)return;V("Cannot register type '"+e+"' twice")}J[n]=t,delete nl[n],nf.hasOwnProperty(n)&&(t=nf[n],delete nf[n],t.forEach(n=>n()))}function nd(n){V(n.L.O.M.name+" instance already deleted")}function ny(){}function nm(n,t,r){if(void 0===n[t].R){var e=n[t];n[t]=function(){return n[t].R.hasOwnProperty(arguments.length)||V("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[t].R+")!"),n[t].R[arguments.length].apply(this,arguments)},n[t].R=[],n[t].R[e.Y]=e}}function nE(n,t,r,e,a,i,o,s){this.name=n,this.constructor=t,this.W=r,this.V=e,this.P=a,this.oa=i,this.aa=o,this.ma=s,this.ia=[]}function ng(n,t,r){for(;t!==r;)t.aa||V("Expected null or instance of "+r.name+", got an instance of "+t.name),n=t.aa(n),t=t.P;return n}function n_(n,t){return null===t?(this.da&&V("null is not a valid "+this.name),0):(t.L||V('Cannot pass "'+nC(t)+'" as a '+this.name),t.L.N||V("Cannot pass deleted object as a pointer of type "+this.name),ng(t.L.N,t.L.O.M,this.M))}function nT(n,t){if(null===t){if(this.da&&V("null is not a valid "+this.name),this.ca){var r=this.ea();return null!==n&&n.push(this.V,r),r}return 0}if(t.L||V('Cannot pass "'+nC(t)+'" as a '+this.name),t.L.N||V("Cannot pass deleted object as a pointer of type "+this.name),!this.ba&&t.L.O.ba&&V("Cannot convert argument of type "+(t.L.T?t.L.T.name:t.L.O.name)+" to parameter type "+this.name),r=ng(t.L.N,t.L.O.M,this.M),this.ca)switch(void 0===t.L.S&&V("Passing raw pointer to smart pointer is illegal"),this.Aa){case 0:t.L.T===this?r=t.L.S:V("Cannot convert argument of type "+(t.L.T?t.L.T.name:t.L.O.name)+" to parameter type "+this.name);break;case 1:r=t.L.S;break;case 2:if(t.L.T===this)r=t.L.S;else{var e=t.clone();r=this.wa(r,G(function(){e.delete()})),null!==n&&n.push(this.V,r)}break;default:V("Unsupporting sharing policy")}return r}function nL(n,t){return null===t?(this.da&&V("null is not a valid "+this.name),0):(t.L||V('Cannot pass "'+nC(t)+'" as a '+this.name),t.L.N||V("Cannot pass deleted object as a pointer of type "+this.name),t.L.O.ba&&V("Cannot convert argument of type "+t.L.O.name+" to parameter type "+this.name),ng(t.L.N,t.L.O.M,this.M))}function nA(n,t,r,e){this.name=n,this.M=t,this.da=r,this.ba=e,this.ca=!1,this.V=this.wa=this.ea=this.ja=this.Aa=this.va=void 0,void 0!==t.P?this.toWireType=nT:(this.toWireType=e?n_:nL,this.U=null)}var nO=[];function nP(n){var t=nO[n];return t||(n>=nO.length&&(nO.length=n+1),nO[n]=t=A.get(n)),t}function nb(n,t){var r,e,a=(n=B(n)).includes("j")?(r=n,e=[],function(){if(e.length=0,Object.assign(e,arguments),r.includes("j")){var n=u["dynCall_"+r];n=e&&e.length?n.apply(null,[t].concat(e)):n.call(null,t)}else n=nP(t).apply(null,e);return n}):nP(t);return"function"!=typeof a&&V("unknown function pointer with signature "+n+": "+t),a}var nN=void 0;function nI(n,t){var r=[],e={};throw t.forEach(function n(t){e[t]||J[t]||(nl[t]?nl[t].forEach(n):(r.push(t),e[t]=!0))}),new nN(n+": "+r.map(q).join([", "]))}function nw(n,t,r,e,a){var i=t.length;2>i&&V("argTypes array size mismatch! Must at least get return value and 'this' types!");var o=null!==t[1]&&null!==r,s=!1;for(r=1;r<t.length;++r)if(null!==t[r]&&void 0===t[r].U){s=!0;break}var u="void"!==t[0].name,c=i-2,f=Array(c),l=[],h=[];return function(){if(arguments.length!==c&&V("function "+n+" called with "+arguments.length+" arguments, expected "+c+" args!"),h.length=0,l.length=o?2:1,l[0]=a,o){var r=t[1].toWireType(h,this);l[1]=r}for(var i=0;i<c;++i)f[i]=t[i+2].toWireType(h,arguments[i]),l.push(f[i]);if(i=e.apply(null,l),s)nu(h);else for(var p=o?1:2;p<t.length;p++){var v=1===p?r:f[p-2];null!==t[p].U&&t[p].U(v)}return u?t[0].fromWireType(i):void 0}}function nS(n,t){for(var r=[],e=0;e<n;e++)r.push(_[t+4*e>>2]);return r}function nR(n){4<n&&0==--M[n].fa&&(M[n]=void 0,F.push(n))}function nC(n){if(null===n)return"null";var t=typeof n;return"object"===t||"array"===t||"function"===t?n.toString():""+n}function nW(n,t){for(var r="",e=0;!(e>=t/2);++e){var a=m[n+2*e>>1];if(0==a)break;r+=String.fromCharCode(a)}return r}function nU(n,t,r){if(void 0===r&&(r=2147483647),2>r)return 0;r-=2;var e=t;r=r<2*n.length?r/2:n.length;for(var a=0;a<r;++a)m[t>>1]=n.charCodeAt(a),t+=2;return m[t>>1]=0,t-e}function nM(n){return 2*n.length}function nF(n,t){for(var r=0,e="";!(r>=t/4);){var a=g[n+4*r>>2];if(0==a)break;++r,65536<=a?(a-=65536,e+=String.fromCharCode(55296|a>>10,56320|1023&a)):e+=String.fromCharCode(a)}return e}function nD(n,t,r){if(void 0===r&&(r=2147483647),4>r)return 0;var e=t;r=e+r-4;for(var a=0;a<n.length;++a){var i=n.charCodeAt(a);if(55296<=i&&57343>=i&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),g[t>>2]=i,(t+=4)+4>r)break}return g[t>>2]=0,t-e}function nk(n){for(var t=0,r=0;r<n.length;++r){var e=n.charCodeAt(r);55296<=e&&57343>=e&&++r,t+=4}return t}var nV={};function nj(n){var t=nV[n];return void 0===t?B(n):t}var nG=[],nY=[],nX=[null,[],[]];k=u.BindingError=D("BindingError"),u.count_emval_handles=function(){for(var n=0,t=5;t<M.length;++t)void 0!==M[t]&&++n;return n},u.get_first_emval=function(){for(var n=5;n<M.length;++n)if(void 0!==M[n])return M[n];return null},Y=u.PureVirtualError=D("PureVirtualError");for(var nB=Array(256),nH=0;256>nH;++nH)nB[nH]=String.fromCharCode(nH);X=nB,u.getInheritedInstanceCount=function(){return Object.keys($).length},u.getLiveInheritedInstances=function(){var n,t=[];for(n in $)$.hasOwnProperty(n)&&t.push($[n]);return t},u.flushPendingDeletes=x,u.setDelayFunction=function(n){z=n,H.length&&z&&z(x)},ne=u.InternalError=D("InternalError"),ny.prototype.isAliasOf=function(n){if(!(this instanceof ny&&n instanceof ny))return!1;var t=this.L.O.M,r=this.L.N,e=n.L.O.M;for(n=n.L.N;t.P;)r=t.aa(r),t=t.P;for(;e.P;)n=e.aa(n),e=e.P;return t===e&&r===n},ny.prototype.clone=function(){if(this.L.N||nd(this),this.L.$)return this.L.count.value+=1,this;var n=no,t=Object,r=t.create,e=Object.getPrototypeOf(this),a=this.L;return n=n(r.call(t,e,{L:{value:{count:a.count,Z:a.Z,$:a.$,N:a.N,O:a.O,S:a.S,T:a.T}}})),n.L.count.value+=1,n.L.Z=!1,n},ny.prototype.delete=function(){this.L.N||nd(this),this.L.Z&&!this.L.$&&V("Object already scheduled for deletion"),Q(this),nt(this.L),this.L.$||(this.L.S=void 0,this.L.N=void 0)},ny.prototype.isDeleted=function(){return!this.L.N},ny.prototype.deleteLater=function(){return this.L.N||nd(this),this.L.Z&&!this.L.$&&V("Object already scheduled for deletion"),H.push(this),1===H.length&&z&&z(x),this.L.Z=!0,this},nA.prototype.pa=function(n){return this.ja&&(n=this.ja(n)),n},nA.prototype.ga=function(n){this.V&&this.V(n)},nA.prototype.argPackAdvance=8,nA.prototype.readValueFromPointer=nc,nA.prototype.deleteObject=function(n){null!==n&&n.delete()},nA.prototype.fromWireType=function(n){function t(){return this.ca?ni(this.M.W,{O:this.va,N:e,T:this,S:n}):ni(this.M.W,{O:this,N:n})}var r,e=this.pa(n);if(!e)return this.ga(n),null;var a=$[Z(this.M,e)];if(void 0!==a)return 0===a.L.count.value?(a.L.N=e,a.L.S=n,a.clone()):(a=a.clone(),this.ga(n),a);if(!(a=nr[a=this.M.oa(e)]))return t.call(this);a=this.ba?a.ka:a.pointerType;var i=function n(t,r,e){return r===e?t:void 0===e.P?null:null===(t=n(t,r,e.P))?null:e.ma(t)}(e,this.M,a.M);return null===i?t.call(this):this.ca?ni(a.M.W,{O:a,N:i,T:this,S:n}):ni(a.M.W,{O:a,N:i})},nN=u.UnboundTypeError=D("UnboundTypeError");var nx={q:function(n,t,r){n=B(n),t=K(t,"wrapper"),r=j(r);var e=[].slice,a=t.M,i=a.W,o=a.P.W,s=a.P.constructor;for(var u in n=U(n,function(){a.P.ia.forEach((function(n){if(this[n]===o[n])throw new Y("Pure virtual function "+n+" must be implemented in JavaScript")}).bind(this)),Object.defineProperty(this,"__parent",{value:i}),this.__construct.apply(this,e.call(arguments))}),i.__construct=function(){this===i&&V("Pass correct 'this' to __construct");var n=s.implement.apply(void 0,[this].concat(e.call(arguments)));Q(n);var t=n.L;n.notifyOnDestruction(),t.$=!0,Object.defineProperties(this,{L:{value:t}}),no(this),n=Z(a,n=t.N),$.hasOwnProperty(n)?V("Tried to register registered instance: "+n):$[n]=this},i.__destruct=function(){this===i&&V("Pass correct 'this' to __destruct"),Q(this);var n=this.L.N;n=Z(a,n),$.hasOwnProperty(n)?delete $[n]:V("Tried to unregister unregistered instance: "+n)},n.prototype=Object.create(i),r)n.prototype[u]=r[u];return G(n)},l:function(n){var t=ns[n];delete ns[n];var r=t.ea,e=t.V,a=t.ha;nh([n],a.map(n=>n.sa).concat(a.map(n=>n.ya)),n=>{var i={};return a.forEach((t,r)=>{var e=n[r],o=t.qa,s=t.ra,u=n[r+a.length],c=t.xa,f=t.za;i[t.na]={read:n=>e.fromWireType(o(s,n)),write:(n,t)=>{var r=[];c(f,n,u.toWireType(r,t)),nu(r)}}}),[{name:t.name,fromWireType:function(n){var t,r={};for(t in i)r[t]=i[t].read(n);return e(n),r},toWireType:function(n,t){for(var a in i)if(!(a in t))throw TypeError('Missing field:  "'+a+'"');var o=r();for(a in i)i[a].write(o,t[a]);return null!==n&&n.push(e,o),o},argPackAdvance:8,readValueFromPointer:nc,U:e}]})},v:function(){},B:function(n,t,r,e,a){var i=np(r);nv(n,{name:t=B(t),fromWireType:function(n){return!!n},toWireType:function(n,t){return t?e:a},argPackAdvance:8,readValueFromPointer:function(n){if(1===r)var e=d;else if(2===r)e=m;else if(4===r)e=g;else throw TypeError("Unknown boolean type size: "+t);return this.fromWireType(e[n>>i])},U:null})},h:function(n,t,r,e,a,i,o,s,c,f,l,h,p){l=B(l),i=nb(a,i),s&&(s=nb(o,s)),f&&(f=nb(c,f)),p=nb(h,p);var v,d=W(l);v=function(){nI("Cannot construct "+l+" due to unbound types",[e])},u.hasOwnProperty(d)?(V("Cannot register public name '"+d+"' twice"),nm(u,d,d),u.hasOwnProperty(void 0)&&V("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),u[d].R[void 0]=v):u[d]=v,nh([n,t,r],e?[e]:[],function(t){if(t=t[0],e)var r,a=t.M,o=a.W;else o=ny.prototype;t=U(d,function(){if(Object.getPrototypeOf(this)!==c)throw new k("Use 'new' to construct "+l);if(void 0===h.X)throw new k(l+" has no accessible constructor");var n=h.X[arguments.length];if(void 0===n)throw new k("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(h.X).toString()+") parameters instead!");return n.apply(this,arguments)});var c=Object.create(o,{constructor:{value:t}});t.prototype=c;var h=new nE(l,t,c,p,a,i,s,f);a=new nA(l,h,!0,!1),o=new nA(l+"*",h,!1,!1);var v=new nA(l+" const*",h,!1,!0);return nr[n]={pointerType:o,ka:v},r=t,u.hasOwnProperty(d)||na("Replacing nonexistant public symbol"),u[d]=r,u[d].Y=void 0,[a,o,v]})},d:function(n,t,r,e,a,i,o){var s=nS(r,e);t=B(t),i=nb(a,i),nh([],[n],function(n){function e(){nI("Cannot call "+a+" due to unbound types",s)}var a=(n=n[0]).name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]);var u=n.M.constructor;return void 0===u[t]?(e.Y=r-1,u[t]=e):(nm(u,t,a),u[t].R[r-1]=e),nh([],s,function(n){return n=nw(a,[n[0],null].concat(n.slice(1)),null,i,o),void 0===u[t].R?(n.Y=r-1,u[t]=n):u[t].R[r-1]=n,[]}),[]})},p:function(n,t,r,e,a,i){0<t||w();var o=nS(t,r);a=nb(e,a),nh([],[n],function(n){var r="constructor "+(n=n[0]).name;if(void 0===n.M.X&&(n.M.X=[]),void 0!==n.M.X[t-1])throw new k("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+n.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return n.M.X[t-1]=()=>{nI("Cannot construct "+n.name+" due to unbound types",o)},nh([],o,function(e){return e.splice(1,0,null),n.M.X[t-1]=nw(r,e,null,a,i),[]}),[]})},a:function(n,t,r,e,a,i,o,s){var u=nS(r,e);t=B(t),i=nb(a,i),nh([],[n],function(n){function e(){nI("Cannot call "+a+" due to unbound types",u)}var a=(n=n[0]).name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&n.M.ia.push(t);var c=n.M.W,f=c[t];return void 0===f||void 0===f.R&&f.className!==n.name&&f.Y===r-2?(e.Y=r-2,e.className=n.name,c[t]=e):(nm(c,t,a),c[t].R[r-2]=e),nh([],u,function(e){return e=nw(a,e,n,i,o),void 0===c[t].R?(e.Y=r-2,c[t]=e):c[t].R[r-2]=e,[]}),[]})},A:function(n,t){nv(n,{name:t=B(t),fromWireType:function(n){var t=j(n);return nR(n),t},toWireType:function(n,t){return G(t)},argPackAdvance:8,readValueFromPointer:nc,U:null})},n:function(n,t,r){r=np(r),nv(n,{name:t=B(t),fromWireType:function(n){return n},toWireType:function(n,t){return t},argPackAdvance:8,readValueFromPointer:function(n,t){switch(t){case 2:return function(n){return this.fromWireType(T[n>>2])};case 3:return function(n){return this.fromWireType(L[n>>3])};default:throw TypeError("Unknown float type: "+n)}}(t,r),U:null})},e:function(n,t,r,e,a){t=B(t),-1===a&&(a=4294967295),a=np(r);var i=n=>n;if(0===e){var o=32-8*r;i=n=>n<<o>>>o}r=t.includes("unsigned")?function(n,t){return t>>>0}:function(n,t){return t},nv(n,{name:t,fromWireType:i,toWireType:r,argPackAdvance:8,readValueFromPointer:function(n,t,r){switch(t){case 0:return r?function(n){return d[n]}:function(n){return y[n]};case 1:return r?function(n){return m[n>>1]}:function(n){return E[n>>1]};case 2:return r?function(n){return g[n>>2]}:function(n){return _[n>>2]};default:throw TypeError("Unknown integer type: "+n)}}(t,a,0!==e),U:null})},b:function(n,t,r){function e(n){n>>=2;var t=_;return new a(t.buffer,t[n+1],t[n])}var a=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];nv(n,{name:r=B(r),fromWireType:e,argPackAdvance:8,readValueFromPointer:e},{ta:!0})},o:function(n,t){var r="std::string"===(t=B(t));nv(n,{name:t,fromWireType:function(n){var t=_[n>>2],e=n+4;if(r)for(var a=e,i=0;i<=t;++i){var o=e+i;if(i==t||0==y[o]){if(a=a?p(y,a,o-a):"",void 0===s)var s=a;else s+="\x00"+a;a=o+1}}else{for(i=0,s=Array(t);i<t;++i)s[i]=String.fromCharCode(y[e+i]);s=s.join("")}return nZ(n),s},toWireType:function(n,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var e,a="string"==typeof t;if(a||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||V("Cannot pass non-string to std::string"),r&&a){var i=0;for(e=0;e<t.length;++e){var o=t.charCodeAt(e);127>=o?i++:2047>=o?i+=2:55296<=o&&57343>=o?(i+=4,++e):i+=3}e=i}else e=t.length;if(o=(i=n$(4+e+1))+4,_[i>>2]=e,r&&a){if(a=o,o=e+1,e=y,0<o){o=a+o-1;for(var s=0;s<t.length;++s){var u=t.charCodeAt(s);if(55296<=u&&57343>=u&&(u=65536+((1023&u)<<10)|1023&t.charCodeAt(++s)),127>=u){if(a>=o)break;e[a++]=u}else{if(2047>=u){if(a+1>=o)break;e[a++]=192|u>>6}else{if(65535>=u){if(a+2>=o)break;e[a++]=224|u>>12}else{if(a+3>=o)break;e[a++]=240|u>>18,e[a++]=128|u>>12&63}e[a++]=128|u>>6&63}e[a++]=128|63&u}}e[a]=0}}else if(a)for(a=0;a<e;++a)255<(s=t.charCodeAt(a))&&(nZ(o),V("String has UTF-16 code units that do not fit in 8 bits")),y[o+a]=s;else for(a=0;a<e;++a)y[o+a]=t[a];return null!==n&&n.push(nZ,i),i},argPackAdvance:8,readValueFromPointer:nc,U:function(n){nZ(n)}})},k:function(n,t,r){if(r=B(r),2===t)var e=nW,a=nU,i=nM,o=()=>E,s=1;else 4===t&&(e=nF,a=nD,i=nk,o=()=>_,s=2);nv(n,{name:r,fromWireType:function(n){for(var r,a=_[n>>2],i=o(),u=n+4,c=0;c<=a;++c){var f=n+4+c*t;(c==a||0==i[f>>s])&&(u=e(u,f-u),void 0===r?r=u:r+="\x00"+u,u=f+t)}return nZ(n),r},toWireType:function(n,e){"string"!=typeof e&&V("Cannot pass non-string to C++ string type "+r);var o=i(e),u=n$(4+o+t);return _[u>>2]=o>>s,a(e,u+4,o+t),null!==n&&n.push(nZ,u),u},argPackAdvance:8,readValueFromPointer:nc,U:function(n){nZ(n)}})},m:function(n,t,r,e,a,i){ns[n]={name:B(t),ea:nb(r,e),V:nb(a,i),ha:[]}},c:function(n,t,r,e,a,i,o,s,u,c){ns[n].ha.push({na:B(t),sa:r,qa:nb(e,a),ra:i,ya:o,xa:nb(s,u),za:c})},C:function(n,t){nv(n,{ua:!0,name:t=B(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},t:function(n,t,r,e,a){n=nG[n],t=j(t),r=nj(r);var i=[];return _[e>>2]=G(i),n(t,r,i,a)},j:function(n,t,r,e){n=nG[n],n(t=j(t),r=nj(r),null,e)},f:nR,g:function(n,t){var r,e,a=function(n,t){for(var r=Array(n),e=0;e<n;++e)r[e]=K(_[t+4*e>>2],"parameter "+e);return r}(n,t),i=a[0],o=nY[t=i.name+"_$"+a.slice(1).map(function(n){return n.name}).join("_")+"$"];if(void 0!==o)return o;var s=Array(n-1);return r=(t,r,e,o)=>{for(var u=0,c=0;c<n-1;++c)s[c]=a[c+1].readValueFromPointer(o+u),u+=a[c+1].argPackAdvance;for(c=0,t=t[r].apply(t,s);c<n-1;++c)a[c+1].la&&a[c+1].la(s[c]);if(!i.ua)return i.toWireType(e,t)},e=nG.length,nG.push(r),o=e,nY[t]=o},r:function(n){4<n&&(M[n].fa+=1)},s:function(n){nu(j(n)),nR(n)},i:function(){w("")},x:function(n,t,r){y.copyWithin(n,t,t+r)},w:function(n){var t=y.length;if(2147483648<(n>>>=0))return!1;for(var r=1;4>=r;r*=2){var e=t*(1+.2/r);e=Math.min(e,n+100663296);var a=Math,i=a.min;e=Math.max(n,e),e+=(65536-e%65536)%65536;n:{var o=l.buffer;try{l.grow(i.call(a,2147483648,e)-o.byteLength+65535>>>16),v();var s=1;break n}catch(n){}s=void 0}if(s)return!0}return!1},z:function(){return 52},u:function(){return 70},y:function(n,t,r,e){for(var a=0,i=0;i<r;i++){var u=_[t>>2],c=_[t+4>>2];t+=8;for(var f=0;f<c;f++){var l=y[u+f],h=nX[n];0===l||10===l?((1===n?o:s)(p(h,0)),h.length=0):h.push(l)}a+=c}return _[e>>2]=a,0}};!function(){function n(n){u.asm=n.exports,l=u.asm.D,v(),A=u.asm.I,P.unshift(u.asm.E),0==--N&&I&&(n=I,I=null,n())}function t(t){n(t.instance)}function e(n){return("function"==typeof fetch?fetch(r,{credentials:"same-origin"}).then(function(n){if(!n.ok)throw"failed to load wasm binary file at '"+r+"'";return n.arrayBuffer()}).catch(function(){return R()}):Promise.resolve().then(function(){return R()})).then(function(n){return WebAssembly.instantiate(n,a)}).then(function(n){return n}).then(n,function(n){s("failed to asynchronously prepare wasm: "+n),w(n)})}var a={a:nx};if(N++,u.instantiateWasm)try{return u.instantiateWasm(a,n)}catch(n){s("Module.instantiateWasm callback failed with error: "+n),f(n)}("function"!=typeof WebAssembly.instantiateStreaming||S()||"function"!=typeof fetch?e(t):fetch(r,{credentials:"same-origin"}).then(function(n){return WebAssembly.instantiateStreaming(n,a).then(t,function(n){return s("wasm streaming compile failed: "+n),s("falling back to ArrayBuffer instantiation"),e(t)})})).catch(f)}();var nz=u.___getTypeName=function(){return(nz=u.___getTypeName=u.asm.F).apply(null,arguments)};function n$(){return(n$=u.asm.H).apply(null,arguments)}function nZ(){return(nZ=u.asm.J).apply(null,arguments)}function nJ(){0<N||(C(O),0<N||e||(e=!0,u.calledRun=!0,h||(C(P),c(u),C(b))))}return u.__embind_initialize_bindings=function(){return(u.__embind_initialize_bindings=u.asm.G).apply(null,arguments)},u.dynCall_jiji=function(){return(u.dynCall_jiji=u.asm.K).apply(null,arguments)},I=function n(){e||nJ(),e||(I=n)},nJ(),t.ready}})();async function initYoga(t){let r=await yoga({instantiateWasm(n,r){WebAssembly.instantiate(t,n).then(n=>{n instanceof WebAssembly.Instance?r(n):r(n.instance)})}});return n(r)}async function initStreaming(t){let r=await yoga({instantiateWasm(n,r){WebAssembly.instantiateStreaming(t,n).then(({instance:n})=>{r(n)})}});return n(r)}export{initYoga as default,initStreaming};
