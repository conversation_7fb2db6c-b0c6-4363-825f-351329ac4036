import E from"./index.js";export{A as ALIGN_AUTO,e as <PERSON>IGN_BASELINE,b as ALIGN_CENTER,c as ALIGN_FLEX_END,a as ALIGN_FLEX_START,g as <PERSON><PERSON><PERSON>_SPACE_AROUND,f as ALIGN_SPACE_BETWEEN,d as ALIGN_STRETCH,h as DIMENSION_HEIGHT,D as DIMENSION_WIDTH,i as DIRECTION_INHERIT,j as DIRECTION_LTR,k as DIRECTION_RTL,l as DISPLAY_FLEX,m as DISPLAY_NONE,u as EDGE_ALL,p as EDGE_BOTTOM,r as EDGE_END,s as EDGE_HORIZONTAL,E as EDGE_LEFT,o as EDGE_RIGHT,q as EDGE_START,n as EDGE_TOP,t as EDGE_VERTICAL,x as EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE,y as EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN,v as <PERSON>X<PERSON>ERIMENTAL_FEATURE_WEB_FLEX_BASIS,F as FLEX_DIRECTION_COLUMN,z as FLEX_DIRECTION_COLUMN_REVERSE,B as FLEX_DIRECTION_ROW,C as FLEX_DIRECTION_ROW_REVERSE,I as GUTTER_ALL,G as GUTTER_COLUMN,H as GUTTER_ROW,K as JUSTIFY_CENTER,L as JUSTIFY_FLEX_END,J as JUSTIFY_FLEX_START,N as JUSTIFY_SPACE_AROUND,M as JUSTIFY_SPACE_BETWEEN,O as JUSTIFY_SPACE_EVENLY,S as LOG_LEVEL_DEBUG,P as LOG_LEVEL_ERROR,U as LOG_LEVEL_FATAL,R as LOG_LEVEL_INFO,T as LOG_LEVEL_VERBOSE,Q as LOG_LEVEL_WARN,X as MEASURE_MODE_AT_MOST,W as MEASURE_MODE_EXACTLY,V as MEASURE_MODE_UNDEFINED,Y as NODE_TYPE_DEFAULT,Z as NODE_TYPE_TEXT,$ as OVERFLOW_HIDDEN,a0 as OVERFLOW_SCROLL,_ as OVERFLOW_VISIBLE,a3 as POSITION_TYPE_ABSOLUTE,a2 as POSITION_TYPE_RELATIVE,a1 as POSITION_TYPE_STATIC,a6 as PRINT_OPTIONS_CHILDREN,a4 as PRINT_OPTIONS_LAYOUT,a5 as PRINT_OPTIONS_STYLE,aa as UNIT_AUTO,a9 as UNIT_PERCENT,a8 as UNIT_POINT,a7 as UNIT_UNDEFINED,ab as WRAP_NO_WRAP,ac as WRAP_WRAP,ad as WRAP_WRAP_REVERSE}from"./wrapAsm-f766f97f.js";let Yoga=await E(await fetch(new URL("./yoga.wasm",import.meta.url)).then(E=>E.arrayBuffer()));export{Yoga as default};
