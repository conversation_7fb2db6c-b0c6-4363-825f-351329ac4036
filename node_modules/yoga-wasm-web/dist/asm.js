import{w as r}from"./wrapAsm-f766f97f.js";export{A as ALIGN_AUTO,e as ALIGN_BASELINE,b as ALIGN_CENTER,c as ALIGN_FLEX_END,a as ALIGN_FLEX_START,g as ALIG<PERSON>_SPACE_AROUND,f as ALIGN_SPACE_BETWEEN,d as ALIGN_STRETCH,h as DIMENSION_HEIGHT,D as DIMENSION_WIDTH,i as DIRECTION_INHERIT,j as DIRECTION_LTR,k as DIRECTION_RTL,l as DISPLAY_FLEX,m as DISPLAY_NONE,u as EDGE_ALL,p as EDGE_BOTTOM,r as EDGE_END,s as EDGE_HORIZONTAL,E as EDGE_LEFT,o as EDGE_RIGHT,q as EDGE_START,n as EDGE_TOP,t as EDGE_VERTICAL,x as EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE,y as EX<PERSON><PERSON>IMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN,v as EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS,F as FLEX_DIRECTION_COLUMN,z as FLEX_DIRECTION_COLUMN_REVERSE,B as FLEX_DIRECTION_ROW,C as FLEX_DIRECTION_ROW_REVERSE,I as GUTTER_ALL,G as GUTTER_COLUMN,H as GUTTER_ROW,K as JUSTIFY_CENTER,L as JUSTIFY_FLEX_END,J as JUSTIFY_FLEX_START,N as JUSTIFY_SPACE_AROUND,M as JUSTIFY_SPACE_BETWEEN,O as JUSTIFY_SPACE_EVENLY,S as LOG_LEVEL_DEBUG,P as LOG_LEVEL_ERROR,U as LOG_LEVEL_FATAL,R as LOG_LEVEL_INFO,T as LOG_LEVEL_VERBOSE,Q as LOG_LEVEL_WARN,X as MEASURE_MODE_AT_MOST,W as MEASURE_MODE_EXACTLY,V as MEASURE_MODE_UNDEFINED,Y as NODE_TYPE_DEFAULT,Z as NODE_TYPE_TEXT,$ as OVERFLOW_HIDDEN,a0 as OVERFLOW_SCROLL,_ as OVERFLOW_VISIBLE,a3 as POSITION_TYPE_ABSOLUTE,a2 as POSITION_TYPE_RELATIVE,a1 as POSITION_TYPE_STATIC,a6 as PRINT_OPTIONS_CHILDREN,a4 as PRINT_OPTIONS_LAYOUT,a5 as PRINT_OPTIONS_STYLE,aa as UNIT_AUTO,a9 as UNIT_PERCENT,a8 as UNIT_POINT,a7 as UNIT_UNDEFINED,ab as WRAP_NO_WRAP,ac as WRAP_WRAP,ad as WRAP_WRAP_REVERSE}from"./wrapAsm-f766f97f.js";var yoga=(()=>{var r="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(e={}){u||(u=void 0!==e?e:{}),u.ready=new Promise(function(r,e){s=r,A=e});var a,i,n=Object.assign({},u),f="";"undefined"!=typeof document&&document.currentScript&&(f=document.currentScript.src),r&&(f=r),f=0!==f.indexOf("blob:")?f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var t=u.print||console.log.bind(console),b=u.printErr||console.warn.bind(console);function o(){}function k(r){this.exports=function(r){for(var e,a=new Uint8Array(123),i=25;i>=0;--i)a[48+i]=52+i,a[65+i]=i,a[97+i]=26+i;function n(r,e,i){for(var n,f,t=0,b=e,o=i.length,k=e+(3*o>>2)-("="==i[o-2])-("="==i[o-1]);t<o;t+=4)n=a[i.charCodeAt(t+1)],f=a[i.charCodeAt(t+2)],r[b++]=a[i.charCodeAt(t)]<<2|n>>4,b<k&&(r[b++]=n<<4|f>>2),b<k&&(r[b++]=f<<6|a[i.charCodeAt(t+3)])}a[43]=62,a[47]=63;var f=new ArrayBuffer(16),t=new Int32Array(f),b=new Float32Array(f),o=new Float64Array(f);function k(){throw Error("abort")}function c(r){b[2]=r}function u(){return b[2]}return function(r){var a,i=r.a,f=i.a,b=f.buffer;f.grow=function(r){r|=0;var a=0|eU(),i=a+r|0;if(a<i&&i<65536){var n=new ArrayBuffer(N(i,65536));new Int8Array(n).set(s),s=new Int8Array(n),A=new Int16Array(n),l=new Int32Array(n),d=new Uint8Array(n),h=new Uint16Array(n),v=new Uint32Array(n),p=new Float32Array(n),m=new Float64Array(n),b=n,f.buffer=b,e=d}return a};var s=new Int8Array(b),A=new Int16Array(b),l=new Int32Array(b),d=new Uint8Array(b),h=new Uint16Array(b),v=new Uint32Array(b),p=new Float32Array(b),m=new Float64Array(b),N=Math.imul,w=Math.fround,y=Math.abs,E=Math.clz32,G=Math.min,R=Math.max,g=i.b,Z=i.c,W=i.d,V=i.e,B=i.f,T=i.g,I=i.h,X=i.i,O=i.j,L=i.k,_=i.l,F=i.m,C=i.n,S=i.o,P=i.p,Y=i.q,U=i.r,M=i.s,H=i.t,Q=i.u,z=i.v,j=i.w,D=i.x,x=i.y,J=i.z,K=i.A,q=i.B,$=i.C,rr=i.D,re=73632,ra=0;function ri(r,e,a,i,n,f,b,o,c,A,h,v,m,N){var E,G,R,g,Z,W,V,B,I=0,X=w(0),O=w(0),L=0,_=0,F=0,C=w(0),S=w(0),P=w(0),Y=0,U=0,M=0,H=w(0),Q=w(0),z=w(0),j=0,D=w(0),x=0,J=w(0),K=w(0),q=0,$=0,rr=0,ra=0,ri=0,rn=w(0),rf=0,rb=w(0),rk=0,rA=0,rl=0,rd=0,rh=w(0),rp=0,rN=0,rw=0,rG=0,rX=w(0),rL=0,r_=0,rF=0,rC=w(0),rY=w(0),rH=0,rz=w(0),rD=w(0),rx=w(0),rJ=0,rK=0,rq=w(0),r$=0,r0=w(0),r2=w(0),r1=w(0),r4=w(0),r3=0,r8=0,r9=0,r7=0,er=0,ei=0,ef=0,et=0,eb=0,eo=w(0),ek=w(0);re=I=re-384|0;r:{e:{a:{if(!(n?e!=e:0)){if(!(f?a!=a:0)){if(l[(L=(c?0:4)+h|0)>>2]=l[L>>2]+1,L=3&l[r+24>>2],r$=(0|i)<=1?1:i,rf=L||r$,s[r+300|0]=252&d[r+300|0]|3&rf,L=r+252|0,i=((0|rf)!=1)<<3,S=rP(r,rL=(0|rf)==2?3:2,b),p[L+i>>2]=S,_=L,L=((0|rf)==1)<<3,Q=rS(r,rL,b),p[_+L>>2]=Q,H=rP(r,0,b),p[r+256>>2]=H,C=rS(r,0,b),p[r+264>>2]=C,eb=(F=r+268|0)+i|0,eo=rM(r,rL),p[eb>>2]=eo,eb=L+F|0,eo=rU(r,rL),p[eb>>2]=eo,X=rM(r,0),p[r+272>>2]=X,P=rU(r,0),p[r+280>>2]=P,eb=(_=i)+(i=r+284|0)|0,eo=rZ(r,rL,b),p[eb>>2]=eo,eb=i+L|0,eo=rW(r,rL,b),p[eb>>2]=eo,O=rZ(r,0,b),p[r+288>>2]=O,D=rW(r,0,b),p[r+296>>2]=D,S=w(S+Q),Q=w(H+C),i=l[r+8>>2]){H=w(w(w(p[r+284>>2]+p[r+292>>2])+p[r+268>>2])+p[r+276>>2]),a=f?w(a-Q):w(NaN),X=w(w(O+D)+X),e=O=n?w(e-S):w(NaN);i:if(e==e){if((e=w(O-H))!=e){e=w(0);break i}e=eu(e,w(0))}S=w(X+P),X=a;n:if(a==a){if((X=w(a-S))!=X){X=w(0);break n}X=eu(X,w(0))}if(!((0|n)!=1|(0|f)!=1)){i=(e=ro(r,2,O,b))!=e,X=w(w(rZ(r,2,b)+rM(r,2))+w(rW(r,2,b)+rU(r,2)));f:{if(!(i|X!=X)){e=eu(e,X);break f}e=i?X:e}p[r+516>>2]=e,i=(e=ro(r,0,a,o))!=e,a=w(w(rZ(r,0,b)+rM(r,0))+w(rW(r,0,b)+rU(r,0)));t:{if(!(i|a!=a)){e=eu(e,a);break t}e=i?a:e}p[r+520>>2]=e;break r}b:{if(16&d[r+4|0]){eY[0|i](I+24|0,r,e,n,X,f,0);break b}eY[0|i](I+24|0,r,e,n,X,f)}l[h+20>>2]=l[h+20>>2]+1,l[(i=(N<<2)+h|0)+24>>2]=l[i+24>>2]+1,e=w(H+p[I+24>>2]),i=(e=ro(r,2,n?(0|n)==2?e:O:e,b))!=e,X=w(w(rZ(r,2,b)+rM(r,2))+w(rW(r,2,b)+rU(r,2)));o:{if(!(i|X!=X)){e=eu(e,X);break o}e=i?X:e}p[r+516>>2]=e,e=w(S+p[I+28>>2]),i=(e=ro(r,0,f?(0|f)==2?e:a:e,o))!=e,a=w(w(rZ(r,0,b)+rM(r,0))+w(rW(r,0,b)+rU(r,0)));k:{if(!(i|a!=a)){e=eu(e,a);break k}e=i?a:e}p[r+520>>2]=e;break r}if(i=l[r+556>>2],N=l[r+560>>2],(0|i)==(0|N)){Q=w(a-Q),i=(e=ro(r,2,-3&n?w(e-S):w(w(w(p[r+284>>2]+p[r+292>>2])+p[r+268>>2])+p[r+276>>2]),b))!=e,a=w(w(rZ(r,2,b)+rM(r,2))+w(rW(r,2,b)+rU(r,2)));c:{if(!(i|a!=a)){e=eu(e,a);break c}e=i?a:e}p[r+516>>2]=e,i=(e=ro(r,0,-3&f?Q:w(w(w(O+D)+X)+P),o))!=e,a=w(w(rZ(r,0,b)+rM(r,0))+w(rW(r,0,b)+rU(r,0)));u:{if(!(i|a!=a)){e=eu(e,a);break u}e=i?a:e}p[r+520>>2]=e;break r}s:if(!c){if(O=w(e-S),X=w(a-Q),!((0|n)==2&O<=w(0)|!(!(X<=w(0))|(0|f)!=2)&X==X)&((0|n)!=1|(0|f)!=1))break s;i=(e=ro(r,2,O==O?(0|n)==2&&O<w(0)?w(0):O:w(0),b))!=e,a=w(w(rZ(r,2,b)+rM(r,2))+w(rW(r,2,b)+rU(r,2)));A:{if(!(i|a!=a)){e=eu(e,a);break A}e=i?a:e}p[r+516>>2]=e,i=(e=ro(r,0,X!=X?w(0):(0|f)==2&&X<w(0)?w(0):X,o))!=e,a=w(w(rZ(r,0,b)+rM(r,0))+w(rW(r,0,b)+rU(r,0)));l:{if(!(i|a!=a)){e=eu(e,a);break l}e=i?a:e}p[r+520>>2]=e;break r}for(rd=(rr=N-i|0)>>2;L=l[i>>2],l[L+552>>2]!=(0|r)&&(F=l[r+568>>2],L=rV(l[F>>2],d[F+8|0],L,r,U,0),l[i>>2]=L,l[L+552>>2]=r),U=U+1|0,(0|N)!=(0|(i=i+4|0)););s[r+300|0]=251&d[r+300|0],U=3,i=(rH=l[r+24>>2])>>>2&3;d:{h:{v:{if((0|rf)==2){N=0;p:switch(i-2|0){case 0:break d;case 1:break p;default:break v}U=2;break d}if(U=2,N=0,i>>>0>1)break h}N=U}U=i}r0=w(e-S),rh=w(w(rZ(r,U,b)+rM(r,U))+w(rW(r,U,b)+rU(r,U))),D=w(rZ(r,N,b)+rM(r,N)),rb=w(D+w(rW(r,N,b)+rU(r,N))),C=rs(r,0,r0,(rk=U>>>0>1)?rh:rb,b),J=rs(r,1,r2=w(a-Q),rk?rb:rh,o),K=rk?J:C,Q=rk?C:J,rp=l[r+560>>2],L=l[r+556>>2],x=rk?n:f;m:if((0|x)==1){if((0|L)==(0|rp))break a;for(i=0,F=L;;){_=i,i=l[F>>2];N:{if(!rQ(i)){i=_;break N}if(Y=0,_||!(_=l[i+552>>2]))break m;w:{y:{a=p[i+32>>2];E:if((a==a||(X=w(0),(a=p[i+28>>2])>w(0)))&&(X=a,a!=a))break y;if(w(y(X))<w(9999999747378752e-20))break m;if(_)break y;a=w(0);break w}if((a=p[i+36>>2])!=a){if(s[i+4|0]<0){a=w(1);break w}if(a=w(0),!((e=p[i+28>>2])<w(0)))break w;a=w(-e)}if(a!=a)break N}if(w(y(a))<w(9999999747378752e-20))break m}if(Y=i,(0|rp)==(0|(F=F+4|0)))break}}if((0|L)==(0|rp))break a;for(ra=(0|rf)<=1?1:rf,rN=(rw=C!=C)|(0|n)!=1,rG=J==J,r_=C==C,X=w(0);;){M=l[L>>2],rc(M),F=l[M+24>>2];G:{if(4194304&F){if(function r(e,a){var i=0,n=0,f=0,t=0,b=0,o=0;for(re=i=re-320|0,l[(i=n=ry(i,0,288))+16>>2]=2143289344,l[i+20>>2]=2143289344,ry(i+24|0,0,49),l[i+80>>2]=2143289344,t=i+288|0,f=i+96|0;l[(i=f)+16>>2]=-1082130432,l[i+20>>2]=-1082130432,l[i+8>>2]=0,l[i+12>>2]=0,l[i>>2]=-1082130432,l[i+4>>2]=-1082130432,(0|t)!=(0|(f=i+24|0)););if(l[n+312>>2]=-1082130432,l[n+316>>2]=-1082130432,l[n+304>>2]=0,l[n+308>>2]=0,l[n+296>>2]=-1082130432,l[n+300>>2]=-1082130432,l[n+288>>2]=2143289344,l[n+292>>2]=2143289344,ru(e+228|0,n,320),l[e+244>>2]=0,l[e+248>>2]=0,s[e+4|0]=1|d[e+4|0],f=l[e+556>>2],b=l[e+560>>2],(0|f)!=(0|b))for(;i=l[f>>2],l[i+552>>2]!=(0|e)&&(t=l[e+568>>2],i=rV(l[t>>2],d[t+8|0],i,e,o,a),l[f>>2]=i,l[i+552>>2]=e,i=l[f>>2]),r(i,a),o=o+1|0,(0|b)!=(0|(f=f+4|0)););re=n+320|0}(M,0),F=1|(i=d[M+4|0]),s[M+4|0]=F,!(4&i))break G;s[M+4|0]=251&F;break G}if(c&&(rO(M,(i=3&F)||ra,Q,K,C),F=l[M+24>>2]),(196608&F)==131072)break G;R:{if((0|Y)==(0|M)){l[Y+308>>2]=0,l[Y+304>>2]=m,a=w(0);break R}_=(rF=l[r+24>>2])>>>2&3;g:{Z:if((0|rf)==2){i=3;W:switch(_-2|0){case 0:break g;case 1:break W;default:break Z}i=2;break g}i=_}O=(rA=i>>>0>1)?C:J,a=w(0),e=p[M+40>>2];V:{B:{_=l[M+40>>2];T:if((0|_)!=2139156720){if((0|_)==2140081935)break B;j=4276;I:{if((0|_)!=2141891242){if(e==e)break I;j=4268}a=p[j>>2];X:{O:switch(0|(_=l[j+4>>2])){case 0:case 3:break O;default:break X}if(e=w(NaN),!(p[M+28>>2]>w(0)))break V;ri=(j=s[M+4|0]<0)?2143289344:0,_=j?3:1,E=ri,t[2]=E,a=u()}switch(e=w(NaN),_-1|0){case 0:break B;case 1:break T;default:break V}}if(G=(-1073741825&_)+536870912|0,t[2]=G,a=u(),!(1073741824&_))break B}e=w(w(O*a)*w(.009999999776482582));break V}e=a}a=p[M+572>>2],_=0;L:{_:switch(0|($=l[M+576>>2])){case 0:case 3:break L;default:break _}if(S=p[M+572>>2],!((0|$)!=1|S!=S)){if(a<w(0))break L;_=1;break L}if(_=1,(0|$)!=2|S!=S||(_=0,a<w(0)))break L;_=r_}S=p[M+580>>2],j=0;F:{C:switch(0|(q=l[M+584>>2])){case 0:case 3:break F;default:break C}if(P=p[M+580>>2],!((0|q)!=1|P!=P)){if(S<w(0))break F;j=1;break F}if(j=1,(0|q)!=2|P!=P||(j=0,S<w(0)))break F;j=rG}S:{P:{if(!(e!=e|O!=O)){if(a=p[M+308>>2],(!d[l[M+568>>2]+20|0]|l[M+304>>2]==(0|m))&a==a)break S;if(!((a=w(w(rZ(M,i,C)+rM(M,i))+w(rW(M,i,C)+rU(M,i))))<=e)&e<a)break P;a=e;break P}if(_&rA){O=w(w(rZ(M,2,C)+rM(M,2))+w(rW(M,2,C)+rU(M,2))),e=w(NaN);Y:switch($-1|0){case 1:a=w(w(C*a)*w(.009999999776482582));case 0:if(e=a,O<=e)break P;break;default:break Y}if(!(e==e|O==O)){a=e;break P}if(e<O){a=O;break P}a=e!=e?O:e;break P}if(!(rA|1^j)){e=w(w(rZ(M,0,C)+rM(M,0))+w(rW(M,0,C)+rU(M,0))),a=w(NaN);U:switch(q-1|0){case 1:S=w(w(J*S)*w(.009999999776482582));case 0:if(a=S,e<=a)break P;break;default:break U}if(a!=a&e!=e)break P;if(e>a){a=e;break P}a=a!=a?e:a;break P}if(l[I+360>>2]=2143289344,l[I+24>>2]=2143289344,ri=0,l[I+356>>2]=0,l[I+352>>2]=0,P=w(rP(M,2,C)+rS(M,2,C)),H=rP(M,0,C),z=rS(M,0,C),O=w(NaN),rl=0,e=w(NaN),_){M:{H:switch($-1|0){case 0:e=a;break M;case 1:break H;default:break M}e=w(w(C*a)*w(.009999999776482582))}l[I+356>>2]=1,e=w(P+e),p[I+24>>2]=e,rl=1}if(H=w(H+z),j){a=w(NaN);Q:{z:switch(q-1|0){case 0:a=S;break Q;case 1:break z;default:break Q}a=w(w(J*S)*w(.009999999776482582))}l[I+352>>2]=1,O=w(H+a),p[I+360>>2]=O,ri=1}$=i>>>0<2,q=3145728&rF;j:{D:{x:{if(!(!rA&(0|q)==2097152)){if(rw|(0|q)==2097152)break D;if(e!=e)break x;break D}if(rw|e==e)break j}rl=2,l[I+356>>2]=2,p[I+24>>2]=C,e=C}J:{if(!(!$&(0|q)==2097152)){if((0|q)==2097152|J!=J)break j;if(O!=O)break J;break j}if(O==O|J!=J)break j}ri=2,l[I+352>>2]=2,p[I+360>>2]=J,O=J}q=(a=p[M+224>>2])!=a;K:if(!q){q:{if(!(rA|(0|rl)!=1)){l[I+352>>2]=1,p[I+360>>2]=H+w(w(e-P)/a);break q}if($|(0|ri)!=1)break K;l[I+356>>2]=1,p[I+24>>2]=w(w(O-H)*a)+P}ri=1,rl=1}F=(F=F>>>13&7)||rF>>>10&7;$:if(!((0|F)==5|(0|rl)==1|(rA|(_|rN))|(0|F)!=4)){if(l[I+356>>2]=1,p[I+24>>2]=C,q)break $;ri=1,l[I+352>>2]=1,p[I+360>>2]=w(C-P)/a}rr:if(!($|(0|f)!=1|J!=J|j|(0|F)!=4|(0|ri)==1)){if(l[I+352>>2]=1,p[I+360>>2]=J,q)break rr;l[I+356>>2]=1,p[I+24>>2]=w(J-H)*a}if(rg(M,2,C,C,I+356|0,I+24|0),rg(M,0,J,C,I+352|0,I+360|0),rt(M,p[I+24>>2],p[I+360>>2],rf,l[I+356>>2],l[I+352>>2],C,J,0,5,A,h,v,m),F=(e=p[(M+(l[(i<<2)+4252>>2]<<2)|0)+516>>2])!=e,a=w(w(rZ(M,i,C)+rM(M,i))+w(rW(M,i,C)+rU(M,i))),!(F|a!=a)){a=eu(e,a);break P}a=F?a:e}p[M+308>>2]=a}l[M+304>>2]=m}X=w(X+w(a+w(rP(M,U,C)+rS(M,U,C))))}if((0|rp)==(0|(L=L+4|0)))break}break e}l[I>>2]=3023,rB(r,I),eX(),k()}l[I+16>>2]=3105,rB(r,I+16|0),eX(),k()}X=w(0)}e=w(X+w(0)),rz=rk?o:b,rC=rk?b:o,rr>>>0>=5&&(e=w(w(rv(r,U,K)*w(rd-1>>>0))+e)),i=e>Q,rp=786432&rH,rG=(0|x)==2&&rp&&i?1:x,r8=(rw=(0|(M=rk?f:n))==1)&(1^c),rk=U>>>0<2,r9=-3&M,rF=r+208|0,q=(f=N<<2)+4220|0,r7=!rp&rw,er=(0|M)!=1|c,rA=(L=U<<2)+4220|0,r_=L+4252|0,ei=(rl=K==K)<<1,$=f+4252|0,ef=!x|!i,rH=I+52|0,r1=rv(r,N,K),F=0,i=0,ri=0;re:{for(;;){j=i,ry(I+24|0,0,44),i=l[r+560>>2],f=l[r+556>>2];ra:if((0|i)!=(0|f)){if((0|(i=i-f|0))<0)break re;if(L=en(I+360|0,i>>2,0,rH),i=l[I+44>>2],f=l[I+48>>2]-i|0,f=rm(l[I+364>>2]-f|0,i,f),i=l[I+44>>2],l[I+364>>2]=i,l[I+44>>2]=f,_=l[I+368>>2],Y=l[I+372>>2],f=l[I+48>>2],l[I+368>>2]=f,x=l[I+52>>2],l[I+48>>2]=_,l[I+52>>2]=Y,l[I+372>>2]=x,l[L>>2]=i,(0|i)!=(0|f)&&(l[I+368>>2]=f+((i-f|0)+3&-4)),!i)break ra;rE(i)}f=(Y=l[r+24>>2])>>>2&3;ri:{rn:if((0|((i=3&Y)||r$))==2){i=3;rf:switch(f-2|0){case 0:break ri;case 1:break rf;default:break rn}i=2;break ri}i=f}X=rv(r,i,C),L=F,_=l[r+560>>2],f=l[r+556>>2];rt:if(!(L>>>0>=_-f>>2>>>0))for(x=786432&Y,a=w(0);;){if(_-f>>2>>>0<=L>>>0)break re;if(Y=l[(L<<2)+f>>2],!(4194304&(rr=l[Y+24>>2])|(196608&rr)==131072)){if(l[Y+548>>2]=j,e=rP(Y,i,C),O=rS(Y,i,C),f=l[I+24>>2],S=(0|L)==(0|F)?w(0):X,e=w(e+O),O=p[Y+308>>2],P=ro(Y,i,O,rC),!(f?!(w(S+w(e+w(a+P)))>Q)|!x:1))break rt;if(l[I+24>>2]=f+1,S=w(S+w(e+P)),p[I+28>>2]=S+p[I+28>>2],rQ(Y)){rb:{if(l[Y+552>>2]){if((e=p[Y+32>>2])!=e&&(e=(e=p[Y+28>>2])>w(0)?e:w(0)),p[I+32>>2]=e+p[I+32>>2],(e=p[Y+36>>2])==e)break rb;if(s[Y+4|0]<0){e=w(1);break rb}if(e=w(0),!((P=p[Y+28>>2])<w(0)))break rb;e=w(-P);break rb}e=w(0),p[I+32>>2]=p[I+32>>2]+w(0)}p[I+36>>2]=p[I+36>>2]-w(e*O)}f=l[I+48>>2];ro:{if((0|f)!=l[I+52>>2]){l[f>>2]=Y,l[I+48>>2]=f+4;break ro}if((_=(rr=(f=f-l[I+44>>2]|0)>>2)+1|0)>>>0>=1073741824)break re;if(ra=f>>>1|0,rr=en(I+360|0,f>>>0>=2147483644?1073741823:_>>>0<ra>>>0?ra:_,rr,rH),l[l[I+368>>2]>>2]=Y,l[I+368>>2]=l[I+368>>2]+4,f=l[I+44>>2],_=l[I+48>>2]-f|0,_=rm(l[I+364>>2]-_|0,f,_),f=l[I+44>>2],l[I+364>>2]=f,l[I+44>>2]=_,Y=l[I+368>>2],ra=l[I+372>>2],_=l[I+48>>2],l[I+368>>2]=_,rN=l[I+52>>2],l[I+48>>2]=Y,l[I+52>>2]=ra,l[I+372>>2]=rN,l[rr>>2]=f,(0|f)!=(0|_)&&(l[I+368>>2]=_+((f-_|0)+3&-4)),!f)break ro;rE(f)}f=l[r+556>>2],_=l[r+560>>2],a=w(a+S)}if(!((L=L+1|0)>>>0<_-f>>2>>>0))break}!((z=p[I+32>>2])>w(0))|!(z<w(1))||(l[I+32>>2]=1065353216,z=w(1)),!((rn=p[I+36>>2])>w(0))|!(rn<w(1))||(l[I+36>>2]=1065353216,rn=w(1)),l[I+40>>2]=L,H=p[I+28>>2],x=l[I+24>>2],ri&&rE(ri),S=p[I+56>>2],rr=l[I+48>>2],ri=l[I+44>>2];rk:{rc:{ru:{rs:if((0|rG)!=1){a=w(0),e=p[rF>>2];rA:{rl:{i=l[rF>>2];rd:if((0|i)!=2139156720){if((0|i)==2140081935)break rl;f=4276;rh:{if((0|i)!=2141891242){if(e==e)break rh;f=4268}switch(a=p[f>>2],e=w(NaN),l[f+4>>2]-1|0){case 0:break rl;case 1:break rd;default:break rA}}if(R=(-1073741825&i)+536870912|0,t[2]=R,a=u(),!(1073741824&i))break rl}e=w(w(a*b)*w(.009999999776482582));break rA}e=a}a=w(0),X=p[r+216>>2];rv:{rp:{i=l[r+216>>2];rm:if((0|i)!=2139156720){if((0|i)==2140081935)break rp;f=4276;rN:{if((0|i)!=2141891242){if(X==X)break rN;f=4268}switch(a=p[f>>2],X=w(NaN),l[f+4>>2]-1|0){case 0:break rp;case 1:break rm;default:break rv}}if(g=(-1073741825&i)+536870912|0,t[2]=g,a=u(),!(1073741824&i))break rp}X=w(w(a*b)*w(.009999999776482582));break rv}X=a}a=w(0),O=p[r+212>>2];rw:{ry:{i=l[r+212>>2];rE:if((0|i)!=2139156720){if((0|i)==2140081935)break ry;f=4276;rG:{if((0|i)!=2141891242){if(O==O)break rG;f=4268}switch(a=p[f>>2],P=w(NaN),l[f+4>>2]-1|0){case 0:break ry;case 1:break rE;default:break rw}}if(Z=(-1073741825&i)+536870912|0,t[2]=Z,a=u(),!(1073741824&i))break ry}P=w(w(a*o)*w(.009999999776482582));break rw}P=a}a=w(0),O=p[r+220>>2];rR:{rg:{i=l[r+220>>2];rZ:if((0|i)!=2139156720){if((0|i)==2140081935)break rg;f=4276;rW:{if((0|i)!=2141891242){if(O==O)break rW;f=4268}switch(a=p[f>>2],O=w(NaN),l[f+4>>2]-1|0){case 0:break rg;case 1:break rZ;default:break rR}}if(W=(-1073741825&i)+536870912|0,t[2]=W,a=u(),!(1073741824&i))break rg}O=w(w(a*o)*w(.009999999776482582));break rR}O=a}if((a=w(((i=U>>>0>1)?e:P)-rh))==a&a>H||(a=w((i?X:O)-rh))==a&a<H)break ru;if(d[l[r+568>>2]+11|0])break rs;if(a=H,!l[r+552>>2]|z==w(0))break rc;e=p[r+32>>2];rV:{rB:{if(e==e){a=e;break rB}if(!((a=p[r+28>>2])>w(0)))break rV}if(a!=a){a=Q;break rc}e=p[r+32>>2]}if(e!=e&&(a=H,!((e=p[r+28>>2])>w(0)))||(a=Q,e!=w(0)))break rc;a=H;break rc}a=Q}if(a!=a)break rc;S=w(a-H);break rk}if(!(H<w(0)))break rk;S=w(-H)}if(Q=a,!r8){rT:{if((0|ri)==(0|rr)){P=w(0);break rT}for(O=w(0),f=ri;;){H=p[(i=l[f>>2])+308>>2],a=ro(i,U,H,rC);rI:{if(S<w(0)){Y=l[i+552>>2];rX:{if(!Y){X=w(0);break rX}if((X=p[i+36>>2])==X)break rX;if(s[i+4|0]<0){X=w(1);break rX}if(X=w(0),!((e=p[i+28>>2])<w(0)))break rX;X=w(-e)}if(!((e=w(a*w(-X)))>w(0)|e<w(0)))break rI;_=(e=ro(i,U,X=w(w(w(S/rn)*e)+a),Q))!=e,P=w(w(rZ(i,U,C)+rM(i,U))+w(rW(i,U,C)+rU(i,U)));rO:{if(!(_|P!=P)){e=eu(e,P);break rO}e=_?P:e}if(X!=X|e!=e|e==X)break rI;e=w(e-a);rL:{if(!Y){a=w(0);break rL}if((a=p[i+36>>2])==a)break rL;if(s[i+4|0]<0){a=w(1);break rL}if(a=w(0),!((X=p[i+28>>2])<w(0)))break rL;a=w(-X)}O=w(O+e),rn=w(w(a*H)+rn);break rI}if(!(S>w(0))|!l[i+552>>2]||(X=p[i+32>>2])!=X&&!((X=p[i+28>>2])>w(0))||!(X<w(0)|X>w(0)))break rI;_=(e=ro(i,U,P=w(w(w(S/z)*X)+a),Q))!=e,H=w(w(rZ(i,U,C)+rM(i,U))+w(rW(i,U,C)+rU(i,U)));r_:{if(!(_|H!=H)){e=eu(e,H);break r_}e=_?H:e}if(P!=P|e!=e|e==P)break rI;z=w(z-X),O=w(O+w(e-a))}if((0|rr)==(0|(f=f+4|0)))break}for(H=w(S-O),rD=w(H/rn),rx=w(H/z),rK=!(ra=rw&(!(12&d[r+26|0])|ef)),rN=l[$>>2],P=w(0),_=ri;;){f=l[_>>2],a=ro(f,U,p[f+308>>2],rC);rF:{rC:{if(H<w(0)){rS:{if(!l[f+552>>2]){e=w(0);break rS}if((e=p[f+36>>2])==e)break rS;if(s[f+4|0]<0){e=w(1);break rS}if(e=w(0),!((X=p[f+28>>2])<w(0)))break rS;e=w(-X)}if(X=a,(e=w(a*w(-e)))==w(0))break rF;e=rn==w(0)?w(a+e):w(w(rD*e)+a);break rC}if(X=a,!(H>w(0))|!l[f+552>>2]||(e=p[f+32>>2])!=e&&!((e=p[f+28>>2])>w(0))||!(e<w(0)|e>w(0)))break rF;e=w(w(rx*e)+a)}if(i=(e=ro(f,U,e,Q))!=e,X=w(w(rZ(f,U,C)+rM(f,U))+w(rW(f,U,C)+rU(f,U))),!(i|X!=X)){X=eu(e,X);break rF}X=i?X:e}e=rP(f,U,C),O=rS(f,U,C),z=rP(f,N,C),rq=rS(f,N,C),O=w(e+O),r4=w(X+O),p[I+352>>2]=r4,l[I+344>>2]=1,z=w(z+rq),e=p[f+224>>2];rP:{if(e==e){l[I+348>>2]=1,O=w(r4-O),p[I+356>>2]=z+(rk?w(O*e):w(O/e)),O=p[(Y=f+(rN<<3)|0)+572>>2],i=l[Y+576>>2],e=p[Y+572>>2];break rP}e=p[(Y=f+(rN<<3)|0)+572>>2];rY:{rU:{rM:{rH:{rQ:{rz:{rj:{rD:{rx:{if(!rl){O=p[Y+572>>2],i=l[Y+576>>2];break rx}O=p[Y+572>>2];rJ:{rK:{rq:{r$:switch(0|(i=l[Y+576>>2])){case 0:case 3:break rq;default:break r$}if(!((0|i)!=1|e!=e)){if(!(ra&O<w(0)))break rJ;break rK}if(!(rK|(!(O<w(0))|((0|i)!=2|e!=e))))break rK;break rJ}if(!ra)break rJ}if((Y=l[f+24>>2]>>>13&7)||(Y=l[r+24>>2]>>>10&7),(0|Y)!=4||(ea(I+24|0,f,N),3==l[I+28>>2]))break rJ;if(ee(I+360|0,f,N),3!=l[I+364>>2])break rD}e=O}switch(0|i){case 0:case 3:break rz;default:break rj}}l[I+348>>2]=1;break rU}if(!((0|i)!=1|e!=e)){if((e=O)<w(0))break rz;break rM}if((0|i)!=2|e!=e)break rQ;if(K!=K)break rz;if(!(O<w(0)))break rH}l[I+348>>2]=ei;break rU}e=w(NaN);r0:switch(i-1|0){case 1:break rH;case 0:break r0;default:break rM}e=O;break rM}e=w(w(K*O)*w(.009999999776482582))}e=w(z+e),p[I+356>>2]=e,l[I+348>>2]=(rw|(0|i)!=2)&e==e;break rY}p[I+356>>2]=K}e=O}rg(f,U,Q,C,I+344|0,I+352|0),rg(f,N,K,C,I+348|0,I+356|0);r2:{r1:{r4:{r3:switch(0|i){case 0:case 3:break r4;default:break r3}if(!((0|i)!=1|e!=e)){if(O<w(0))break r4;break r1}if((0|i)!=2|e!=e)break r1;if(O<w(0))break r4;if(Y=0,rl)break r2}if(Y=0,(i=l[f+24>>2]>>>13&7)||(i=l[r+24>>2]>>>10&7),(0|i)!=4||(ea(I+24|0,f,N),3==l[I+28>>2]))break r2;ee(I+360|0,f,N),Y=3!=l[I+364>>2];break r2}Y=0}e=p[I+352>>2],O=p[I+356>>2],z=(i=U>>>0>1)?e:O,e=i?O:e,rJ=l[I+344>>2],r3=l[I+348>>2],et=i?rJ:r3,rJ=i?r3:rJ,i=(1^Y)&c,rt(f,z,e,3&d[r+300|0],et,rJ,C,J,i,i?4:7,A,h,v,m),P=w(P+w(X-a)),i=d[r+300|0];r8:{r6:{if(4&i){Y=251&i;break r6}if(Y=251&i,i=0,!(4&d[f+300|0]))break r8}i=4}if(s[r+300|0]=i|Y,(0|rr)==(0|(_=_+4|0)))break}}S=w(S-P)}i=d[r+300|0],s[r+300|0]=251&i|(S<w(0)?4:4&i),O=w(rZ(r,U,b)+rM(r,U)),rD=w(rW(r,U,b)+rU(r,U)),H=rv(r,U,b);r5:{if(!(!(S>w(0))|(0|rG)!=2)){P=w(0),a=w(0);r9:{r7:{e=p[(i=rF+(l[r_>>2]<<2)|0)>>2],f=(0|(i=l[i>>2]))==2139156720;er:{if(!f){if(_=(0|i)==2140081935)break r7;if((Y=(0|i)==2141891242)|e!=e)break r5;if(_)break r7;if(Y)break r5;if(V=(-1073741825&i)+536870912|0,t[2]=V,a=u(),!(1073741824&i))break er}a=w(w(rC*a)*w(.009999999776482582))}if(a!=a)break r5;a=w(0);ee:if(!f){if((0|i)==2140081935)break r7;f=4276;ea:{if((0|i)!=2141891242){if(e==e)break ea;f=4268}switch(a=p[f>>2],X=w(NaN),l[f+4>>2]-1|0){case 0:break r7;case 1:break ee;default:break r9}}if(B=(-1073741825&i)+536870912|0,t[2]=B,a=u(),!(1073741824&i))break r7}X=w(w(rC*a)*w(.009999999776482582));break r9}X=a}if((e=w(w(w(X-O)-rD)-w(Q-S)))!=e)break r5;P=eu(e,w(0));break r5}P=S}Y=L>>>0<=F>>>0;ei:{if(!Y){for(rr=l[r+556>>2],ra=(i=l[r+560>>2]-rr>>2)>>>0<F>>>0?F:i,_=0,i=F;;){if((0|i)==(0|ra))break re;if(f=l[rr+(i<<2)>>2],(196608&l[f+24>>2])!=131072&&(ea(rN=I+24|0,f,U),rK=l[I+28>>2],ee(rN,f,U),_=(((0|rK)==3)+_|0)+(3==l[I+28>>2])|0),(0|L)==(0|(i=i+1|0)))break}if(z=w(0),a=H,_)break ei}z=w(0),_=0,a=H;en:switch((l[r+24>>2]>>>4&7)-1|0){case 0:z=w(P*w(.5));break ei;case 1:z=P;break ei;case 2:if(x>>>0<2)break ei;a=w(H+w((eo=w(0),ek=eu(P,w(0)),((eb=P!=P)?eo:ek)/w(x-1>>>0))));break ei;case 4:z=w(P/w(x+1>>>0)),a=w(H+z);break ei;case 3:break en;default:break ei}z=w(w(P*w(.5))/w(x>>>0)),a=w(w(z+z)+H)}if(e=w(O+z),S=w(0),x=rj(r),Y)a=w(0);else{for(rr=L-1|0,rx=w(P/w(0|_)),O=w(0),X=w(0),i=F;;){if(f=l[r+556>>2],l[r+560>>2]-f>>2>>>0<=i>>>0)break re;_=(f=l[f+(i<<2)>>2])+228|0,ru(I+24|0,_,320),a=w(a-((0|i)==(0|rr)?H:w(0))),ra=l[f+24>>2];ef:if(!(4194304&ra)){et:{if((196608&ra)==131072){if(!r5(f,U))break et;if(!c)break ef;P=rI(f,U,Q),rn=rM(r,U),rq=rP(f,U,C),p[_+(l[rA>>2]<<2)>>2]=rq+w(P+rn);break ef}if(ea(I+360|0,f,U),e=w(e+(3==l[I+364>>2]?rx:w(-0))),c&&(ra=_,_=l[rA>>2]<<2,p[ra+_>>2]=e+p[_+(I+24|0)>>2]),ee(I+360|0,f,U),e=w(e+(3==l[I+364>>2]?rx:w(-0))),!er){e=w(e+w(w(a+w(rP(f,U,C)+rS(f,U,C)))+p[I+104>>2])),S=K;break ef}if(e=w(e+w(a+w(p[(_=f+516|0)+(l[r_>>2]<<2)>>2]+w(rP(f,U,C)+rS(f,U,C))))),x){P=rR(f),rn=rP(f,0,C),P=w(P+rn),rn=w(w(p[f+520>>2]+w(rP(f,0,C)+rS(f,0,C)))-P),f=O!=O;eb:{if(!(f|P!=P)){O=eu(O,P);break eb}O=f?P:O}if(!((f=X!=X)|rn!=rn)){X=eu(X,rn);break ef}X=f?rn:X;break ef}if(!((P=w(p[_+(l[$>>2]<<2)>>2]+w(rP(f,N,C)+rS(f,N,C))))!=P|(f=S!=S))){S=eu(S,P);break ef}S=f?P:S;break ef}if(!c)break ef;P=p[(f=l[rA>>2]<<2)+(I+24|0)>>2],eb=f+_|0,eo=w(z+w(P+rM(r,U))),p[eb>>2]=eo}if((0|L)==(0|(i=i+1|0)))break}a=w(X+O)}if(O=x?a:S,P=K,!r9){i=(a=ro(r,N,w(rb+O),rz))!=a,X=w(w(rZ(r,N,b)+rM(r,N))+w(rW(r,N,b)+rU(r,N)));eo:{if(!(i|X!=X)){a=eu(a,X);break eo}a=i?X:a}P=w(a-rb)}i=(a=ro(r,N,w(rb+(r7?K:O)),rz))!=a,X=w(w(rZ(r,N,b)+rM(r,N))+w(rW(r,N,b)+rU(r,N)));ek:{if(!(i|X!=X)){a=eu(a,X);break ek}a=i?X:a}if(X=w(a-rb),!(!c|Y))for(;;){if(i=l[r+556>>2],l[r+560>>2]-i>>2>>>0<=F>>>0)break re;i=l[i+(F<<2)>>2],f=l[i+24>>2];ec:if(!(4194304&f)){if((196608&f)==131072){if(f=l[q>>2],r5(i,N)&&(a=w(w(rI(i,N,K)+rM(r,N))+rP(i,N,C)),p[(i+(f<<2)|0)+228>>2]=a,a==a))break ec;eb=i+(f<<2)|0,eo=w(rM(r,N)+rP(i,N,C)),p[eb+228>>2]=eo;break ec}(f=f>>>13&7)||(f=l[r+24>>2]>>>10&7);eu:{es:{eA:{if((0|f)==5){f=8&l[r+24>>2]?5:1;break eA}if((0|f)!=4||(ea(I+24|0,i,N),f=4,3==l[I+28>>2]))break eA;if(ee(I+360|0,i,N),_=l[$>>2],3==l[I+364>>2])break es;O=p[(_=i+(_<<3)|0)+572>>2];el:{ed:switch(0|(f=l[_+576>>2])){case 0:case 3:break el;default:break ed}if(S=p[_+572>>2],!((0|f)!=1|S!=S)){if(a=D,O<w(0))break el;break eu}if(a=D,!(O<w(0))&rl|((0|f)!=2|S!=S))break eu}O=p[(i+(l[r_>>2]<<2)|0)+516>>2],a=(a=p[i+224>>2])==a?w(w(rP(i,N,C)+rS(i,N,C))+(rk?w(O*a):w(O/a))):X,p[I+360>>2]=a,eb=I,eo=w(O+w(rP(i,U,C)+rS(i,U,C))),p[eb+24>>2]=eo,l[I+356>>2]=1,l[I+352>>2]=1,rg(i,U,Q,C,I+356|0,I+24|0),rg(i,N,K,C,I+352|0,I+360|0),O=p[I+360>>2],S=p[I+24>>2],a=(f=U>>>0>1)?O:S,O=f?S:O,f=(0|rp)!=0&(896&l[r+24>>2])!=512,rt(i,O,a,rf,!(f&rk|O!=O),(!f|rk)&a==a,C,J,1,2,A,h,v,m),a=D;break eu}_=l[$>>2]}a=p[((_<<2)+i|0)+516>>2],O=rP(i,N,C),S=rS(i,N,C),ea(I+24|0,i,N),O=w(P-w(a+w(O+S)));eh:if(3==l[I+28>>2]){if(ee(I+360|0,i,N),3!=l[I+364>>2])break eh;a=w(O*w(.5)),a=w(D+(eo=w(0),ek=eu(a,w(0)),(eb=a!=a)?eo:ek));break eu}if(ee(I+24|0,i,N),a=D,3==l[I+28>>2])break eu;if(ea(I+24|0,i,N),3==l[I+28>>2]){a=w(D+(eo=w(0),ek=eu(O,w(0)),(eb=O!=O)?eo:ek));break eu}ev:switch(f-1|0){case 1:a=w(D+w(O*w(.5)));break eu;case 0:break eu;default:break ev}a=w(D+O)}p[(i=i+(l[q>>2]<<2)|0)+228>>2]=a+w(rX+p[i+228>>2])}if((0|(F=F+1|0))==(0|L))break}a=w((j?r1:w(0))+X),i=rY!=rY,e=w(rD+e);ep:{if(!(i|e!=e)){rY=eu(rY,e);break ep}rY=i?e:rY}if(rX=w(rX+a),i=j+1|0,F=L,!(L>>>0<rd>>>0))break}em:if(c&&(rp||rj(r))){O=w(0);eN:{ew:if(K==K){e=w(K-rX);ey:switch((l[r+24>>2]>>>7&7)-2|0){case 0:D=w(D+w(e*w(.5)));break ew;case 2:if(!(K>rX))break ew;O=w(e/w(i>>>0));break ew;case 5:if(K>rX){if(D=w(D+w(e/w(i<<1>>>0))),i>>>0<2)break ew;O=w(e/w(i>>>0));break eN}D=w(D+w(e*w(.5)));break ew;case 4:if(!(K>rX)|i>>>0<2)break ew;O=w(e/w(j>>>0));break eN;case 1:break ey;default:break ew}D=w(D+e)}if(!i)break em}for(_=0,i=0,f=0;;){if(S=w(0),a=w(0),e=w(0),X=w(0),L=i,i>>>0<rd>>>0){eE:{for(;;){if(f=l[r+556>>2],l[r+560>>2]-f>>2>>>0<=L>>>0)break re;F=l[f+(L<<2)>>2],Y=l[F+24>>2];eG:if(!(4194304&Y|(196608&Y)==131072)){if(f=L,l[F+548>>2]!=(0|_))break eE;e=p[(F+(l[$>>2]<<2)|0)+516>>2];eR:if(e>=w(0)){if(f=a!=a,e=w(e+w(rP(F,N,C)+rS(F,N,C))),!(f|e!=e)){a=eu(a,e);break eR}a=f?e:a}if((f=Y>>>13&7)||(f=l[r+24>>2]>>>10&7),!(8&d[r+24|0])|(0|f)!=5)break eG;e=rR(F),P=rP(F,0,C),e=w(e+P),P=w(w(p[F+520>>2]+w(rP(F,0,C)+rS(F,0,C)))-e),f=a!=a,F=X!=X;eg:{if(!(F|e!=e)){X=eu(X,e);break eg}X=F?e:X}F=S!=S;eZ:{if(!(F|P!=P)){S=eu(S,P);break eZ}S=F?P:S}if(!(f|(e=w(X+S))!=e)){a=eu(a,e);break eG}a=f?e:a}if((0|rd)==(0|(L=L+1|0)))break}f=rd}e=a}if(P=w(D+(_?r1:w(0))),a=w(O+e),D=w(P+a),i>>>0<f>>>0)for(H=w(P+X);;){if(L=l[r+556>>2],l[r+560>>2]-L>>2>>>0<=i>>>0)break re;F=l[L+(i<<2)>>2],L=l[F+24>>2];eW:if(!(4194304&L|(196608&L)==131072)){(L=L>>>13&7)||(L=l[r+24>>2]>>>10&7);eV:{eB:switch(L-1|0){case 4:if(8&d[r+24|0])break eV;case 0:e=rP(F,N,C),p[(F+(l[q>>2]<<2)|0)+228>>2]=P+e;break eW;case 2:e=rS(F,N,C),p[(L=F+228|0)+(l[q>>2]<<2)>>2]=w(D-e)-p[(L+(l[$>>2]<<2)|0)+288>>2];break eW;case 1:p[(L=F+228|0)+(l[q>>2]<<2)>>2]=P+w(w(a-p[(L+(l[$>>2]<<2)|0)+288>>2])*w(.5));break eW;case 3:break eB;default:break eW}e=rP(F,N,C),p[(F+(l[q>>2]<<2)|0)+228>>2]=P+e,e=p[(L=Y=F+(l[$>>2]<<3)|0)+572>>2];eT:{eI:switch(0|(L=l[L+576>>2])){case 0:case 3:break eT;default:break eI}if(X=p[Y+572>>2],!((0|L)!=1|X!=X)){if(e<w(0))break eT;break eW}if(!(e<w(0))&rl|((0|L)!=2|X!=X))break eW}eX:{if(U>>>0>=2){S=a,e=p[F+516>>2],X=w(e+w(rP(F,U,C)+rS(F,U,C)));break eX}S=w(p[F+520>>2]+w(rP(F,N,C)+rS(F,N,C))),e=p[F+516>>2],X=a}eO:{eL:{if(!(X!=X|e!=e)){if(w(y(w(X-e)))<w(9999999747378752e-20))break eL;break eO}if(X==X|e==e)break eO}if(!((L=(e=p[F+520>>2])!=e)|S!=S)){if(!(w(y(w(S-e)))<w(9999999747378752e-20)))break eO;break eW}if(S==S)break eO;if(L)break eW}rt(F,X,S,rf,1,1,C,J,1,3,A,h,v,m);break eW}eb=F,eo=w(w(H-rR(F))+rI(F,0,K)),p[eb+232>>2]=eo}if((0|f)==(0|(i=i+1|0)))break}if(L=(0|_)!=(0|j),_=_+1|0,i=f,!L)break}}_=r+516|0,i=(e=ro(r,2,r0,b))!=e,a=w(w(rZ(r,2,b)+rM(r,2))+w(rW(r,2,b)+rU(r,2)));e_:{if(!(i|a!=a)){e=eu(e,a);break e_}e=i?a:e}p[_>>2]=e,i=(e=ro(r,0,r2,o))!=e,a=w(w(rZ(r,0,b)+rM(r,0))+w(rW(r,0,b)+rU(r,0)));eF:{if(!(i|a!=a)){e=eu(e,a);break eF}e=i?a:e}p[r+520>>2]=e;eC:{eS:{eP:if(!rG||!((0|(i=l[r+24>>2]>>>20&3))==2|(0|rG)!=2)){if(i=(e=ro(r,U,rY,rC))!=e,a=w(w(rZ(r,U,b)+rM(r,U))+w(rW(r,U,b)+rU(r,U))),!(i|a!=a)){e=eu(e,a);break eS}e=i?a:e;break eS}if((0|rG)!=2|(0|i)!=2)break eC;i=(a=w(rh+Q))!=a,e=ro(r,U,rY,rC);eY:{if(!(i|e!=e)){e=es(a,e);break eY}e=i?e:a}if(!((i=e!=e)|rh!=rh)){e=eu(e,rh);break eS}e=i?rh:e}p[_+(l[r_>>2]<<2)>>2]=e}eU:{eM:{eH:if(!M||(f=(0|M)!=2,i=l[r+24>>2]>>>20&3,!(f|(0|i)==2))){if(i=(e=ro(r,N,w(rb+rX),rz))!=e,a=w(w(rZ(r,N,b)+rM(r,N))+w(rW(r,N,b)+rU(r,N))),!(i|a!=a)){e=eu(e,a);break eM}e=i?a:e;break eM}if(f|(0|i)!=2)break eU;i=(a=w(rb+K))!=a,e=ro(r,N,w(rb+rX),rz);eQ:{if(!(i|e!=e)){e=es(a,e);break eQ}e=i?e:a}if(!((i=e!=e)|rb!=rb)){e=eu(e,rb);break eM}e=i?rb:e}p[_+(l[$>>2]<<2)>>2]=e}ez:if(c){if(i=l[r+560>>2],F=l[r+556>>2],(786432&l[r+24>>2])==524288){for(f=rd>>>0<=1?1:rd,c=i-F>>2,i=0;L=0,L=i>>>0<c>>>0?l[(i<<2)+F>>2]:L,(196608&l[L+24>>2])!=131072&&(Y=(L=L+228|0)+(l[q>>2]<<2)|0,j=l[$>>2]<<2,p[Y>>2]=w(p[j+_>>2]-p[Y>>2])-p[(L+j|0)+288>>2]),(0|f)!=(0|(i=i+1|0)););F=l[r+556>>2],i=l[r+560>>2]}if((0|i)!=(0|F))for(j=((U>>>0>1?rG:n)|0)!=0;;){c=l[F>>2];ej:if((4390912&l[c+24>>2])==131072){a=C,Q=J,d[l[r+568>>2]+21|0]&&(Q=p[r+520>>2],a=p[r+516>>2]),n=l[r+24>>2]>>>2&3;eD:{ex:{if((0|rf)==2){f=0,L=3;eJ:switch(n-2|0){case 0:break eD;case 1:break eJ;default:break ex}L=2;break eD}if(f=0,n>>>0<=1)break ex;L=n;break eD}L=n,f=rL}o=w(rP(c,2,a)+rS(c,2,a)),O=rP(c,0,a),S=rS(c,0,a),e=p[c+572>>2];eK:{eq:{e$:switch(0|(n=l[c+576>>2])){case 0:case 3:break eq;default:break e$}b=p[c+572>>2];e0:{if(!((0|n)!=1|b!=b)){if(X=e,!(e<w(0)))break e0;break eq}e2:{if(!((0|n)!=2|b!=b)){if(a!=a|e<w(0))break eq;break e2}X=w(NaN);e1:switch(n-1|0){case 1:break e2;case 0:break e1;default:break e0}X=e;break e0}X=w(w(a*e)*w(.009999999776482582))}X=w(o+X);break eK}if(X=w(NaN),!r5(c,2)||!r6(c,2))break eK;if(n=(e=ro(c,2,w(w(p[r+516>>2]-w(rM(r,2)+rU(r,2)))-w(rI(c,2,a)+rT(c,2,a))),a))!=e,b=w(w(rZ(c,2,a)+rM(c,2))+w(rW(c,2,a)+rU(c,2))),!(n|b!=b)){X=eu(e,b);break eK}X=n?b:e}S=w(O+S);e4:{e3:{e8:{e6:{e5:{e9:{e7:{Y=X!=X,b=p[c+580>>2];ar:{ae:{aa:switch(0|(n=l[c+584>>2])){case 0:case 3:break ae;default:break aa}e=p[c+580>>2];ai:{if(!((0|n)!=1|e!=e)){if(!((e=b)<w(0)))break ai;break ae}an:{if(!((0|n)!=2|e!=e)){if(Q!=Q|b<w(0))break ae;break an}e=w(NaN);af:switch(n-1|0){case 1:break an;case 0:break af;default:break ai}e=b;break ai}e=w(w(Q*b)*w(.009999999776482582))}e=w(S+e);break ar}at:if(!(r5(c,0)&&r6(c,0))){if(e=w(NaN),X==X)break e7;break e8}if(n=(e=ro(c,0,w(w(p[r+520>>2]-w(rM(r,0)+rU(r,0)))-w(rI(c,0,Q)+rT(c,0,Q))),Q))!=e,b=w(w(rZ(c,0,a)+rM(c,0))+w(rW(c,0,a)+rU(c,0))),!(n|b!=b)){e=eu(e,b);break ar}e=n?b:e}if((0|Y)==(e!=e|0)||(O=p[c+224>>2])!=O)break e5;if(X!=X){X=w(w(w(e-S)*O)+o);break e5}if(e==e)break e5;break e9}if((O=p[c+224>>2])!=O)break e6}e=w(S+w(w(X-o)/O))}if(X!=X)break e8;if(e==e)break e4}n=0;break e3}n=1}Y=a>w(0),b=(x=n&j&L>>>0<2)&&Y?a:X,n^=1,rt(c,b,e,rf,x&&Y?2:n,e==e,b,e,0,6,A,h,v,m),X=w(p[c+516>>2]+eW(c,2,a)),e=w(p[c+520>>2]+eW(c,0,a))}rt(c,X,e,rf,1,1,X,e,1,1,A,h,v,m),o=(n=L>>>0<2)?a:Q,b=d[(Y=l[r+568>>2])+22|0]?o:a;ab:{ao:{ak:if(!(!r6(c,L)||r5(c,L))){x=l[(L<<2)+4252>>2]<<2,e=w(w(w(w(p[x+_>>2]-p[(c+x|0)+516>>2])-rU(r,L))-rS(c,L,b))-rT(c,L,n?Q:a));break ao}if(!(r5(c,L)|(112&l[r+24>>2])!=16)){n=l[(L<<2)+4252>>2]<<2,e=w(w(p[n+_>>2]-p[(n+c|0)+516>>2])*w(.5));break ao}if(!(r5(c,L)|(112&l[r+24>>2])!=32)){n=l[(L<<2)+4252>>2]<<2,e=w(p[n+_>>2]-p[(n+c|0)+516>>2]);break ao}if(!d[Y+21|0]||!r5(c,L))break ab;e=p[_+(l[(L<<2)+4252>>2]<<2)>>2],e=w(w(rI(c,L,e)+rM(r,L))+rP(c,L,e))}p[(c+(l[(L<<2)+4220>>2]<<2)|0)+228>>2]=e}ac:{au:if(!(!r6(c,f)||r5(c,f))){n=l[(f<<2)+4252>>2]<<2,e=w(w(w(w(p[n+_>>2]-p[(n+c|0)+516>>2])-rU(r,f))-rS(c,f,b))-rT(c,f,o));break ac}as:if(!r5(c,f)){if((n=l[c+24>>2]>>>13&7)||(n=l[r+24>>2]>>>10&7),(0|n)!=2)break as;n=l[(f<<2)+4252>>2]<<2,e=w(w(p[n+_>>2]-p[(n+c|0)+516>>2])*w(.5));break ac}aA:if(!r5(c,f)){L=l[c+24>>2]>>>13&7,n=l[r+24>>2],L=L||n>>>10&7;al:{if(!((0|L)!=5|8&n)){if((786432&n)==524288)break al;break aA}if(((786432&n)==524288|0)==((0|L)==3|0))break aA}n=l[(f<<2)+4252>>2]<<2,e=w(p[n+_>>2]-p[(n+c|0)+516>>2]);break ac}if(!d[Y+21|0]||!r5(c,f))break ej;e=p[_+(l[(f<<2)+4252>>2]<<2)>>2],e=w(w(rI(c,f,e)+rM(r,f))+rP(c,f,e))}p[(c+(l[(f<<2)+4220>>2]<<2)|0)+228>>2]=e}if((0|(F=F+4|0))==(0|i))break}if(!((N|U)&1))break ez;for(f=1&N,c=1&U,A=rd>>>0<=1?1:rd,h=(N<<2)+4236|0,v=(U<<2)+4236|0,m=l[r+556>>2],N=l[r+560>>2]-m>>2,i=0;;){if((0|i)==(0|N))break re;r=l[m+(i<<2)>>2];ad:if(!(64&d[r+26|0])){if(c&&(n=r+228|0,L=l[r_>>2]<<2,p[n+(l[v>>2]<<2)>>2]=w(p[L+_>>2]-p[(n+L|0)+288>>2])-p[n+(l[rA>>2]<<2)>>2]),!f)break ad;r=r+228|0,n=l[$>>2]<<2,p[r+(l[h>>2]<<2)>>2]=w(p[n+_>>2]-p[(r+n|0)+288>>2])-p[r+(l[q>>2]<<2)>>2]}if((0|A)==(0|(i=i+1|0)))break}}if(!ri)break r;rE(ri);break r}T(),k()}re=I+384|0}function rn(){var r=0,e=0;B(6952,6953,6954,0,4284,17,4287,0,4287,0,2528,4289,18),l[(r=eo(8))>>2]=8,l[r+4>>2]=1,g(6952,2873,6,4304,4328,19,0|r,1),B(6956,6957,6958,6952,4284,20,4284,21,4284,22,2e3,4289,23),l[(r=eo(4))>>2]=24,g(6956,2358,2,4336,4344,25,0|r,0),W(6952,1397,2,4348,4356,26,27),W(6952,2978,3,4436,4448,28,29),B(6976,6977,6978,0,4284,30,4287,0,4287,0,2544,4289,31),l[(r=eo(8))>>2]=8,l[r+4>>2]=1,g(6976,3244,2,4456,4344,32,0|r,1),B(6979,6980,6981,6976,4284,33,4284,34,4284,35,2023,4289,36),l[(r=eo(4))>>2]=37,g(6979,2358,2,4464,4344,38,0|r,0),W(6976,1397,2,4472,4356,39,40),W(6976,2978,3,4436,4448,28,41),B(6982,6983,6984,0,4284,42,4287,0,4287,0,2812,4289,43),P(6982,1,4520,4284,44,45),W(6982,2866,1,4520,4284,44,45),W(6982,1128,2,4524,4344,46,47),l[(r=eo(8))+4>>2]=0,l[r>>2]=48,g(6982,3185,4,4544,4560,49,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=50,g(6982,1980,3,4568,4580,51,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=52,g(6982,1951,3,4588,4600,53,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=54,g(6982,1799,3,4588,4600,53,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=55,g(6982,3215,3,4608,4448,56,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=57,g(6982,1925,2,4620,4356,58,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=59,g(6982,1784,2,4620,4356,58,0|r,0),_(6985,1270,4628,60,4289,61),eh(1731,0),eh(1611,8),eh(2144,16),eh(2454,24),eh(2560,32),eh(1617,40),L(6985),_(6955,2849,4628,62,4289,63),ed(2560,0),ed(1617,8),L(6955),_(6986,2860,4628,64,4289,65),l[(r=eo(4))>>2]=8,l[(e=eo(4))>>2]=8,X(6986,2854,7018,4630,66,0|r,7018,4634,67,0|e),l[(r=eo(4))>>2]=0,l[(e=eo(4))>>2]=0,X(6986,1606,7011,4356,68,0|r,7011,4600,69,0|e),L(6986),B(6987,6988,6989,0,4284,70,4287,0,4287,0,2973,4289,71),P(6987,1,4640,4284,72,73),W(6987,1592,1,4640,4284,72,73),W(6987,2802,2,4644,4356,74,75),W(6987,1128,2,4652,4344,76,77),l[(r=eo(8))+4>>2]=0,l[r>>2]=78,g(6987,1752,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=80,g(6987,2956,3,4660,4600,81,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=82,g(6987,2881,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=84,g(6987,2334,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=86,g(6987,1484,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=87,g(6987,1355,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=88,g(6987,1817,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=89,g(6987,2823,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=90,g(6987,2378,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=91,g(6987,2163,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=92,g(6987,1319,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=93,g(6987,2412,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=94,g(6987,1503,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=95,g(6987,2235,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=96,g(6987,1219,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=97,g(6987,1136,3,4672,4600,83,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=98,g(6987,1158,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=100,g(6987,1845,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=101,g(6987,1464,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=102,g(6987,2218,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=103,g(6987,1195,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=104,g(6987,2500,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=105,g(6987,2590,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=106,g(6987,1539,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=107,g(6987,2249,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=108,g(6987,1650,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=109,g(6987,1427,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=110,g(6987,2204,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=111,g(6987,2608,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=112,g(6987,1555,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=113,g(6987,1670,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=114,g(6987,1444,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=115,g(6987,2566,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=116,g(6987,1520,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=117,g(6987,1624,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=118,g(6987,1407,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=119,g(6987,2304,3,4712,4634,99,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=120,g(6987,2092,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=121,g(6987,2718,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=122,g(6987,1574,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=123,g(6987,2187,4,4688,4704,85,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=124,g(6987,2897,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=126,g(6987,2346,3,4732,4448,127,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=128,g(6987,1371,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=129,g(6987,1831,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=130,g(6987,2836,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=131,g(6987,2395,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=132,g(6987,2175,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=133,g(6987,1337,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=134,g(6987,2422,3,4732,4448,127,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=135,g(6987,1858,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=137,g(6987,1207,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=139,g(6987,2514,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=140,g(6987,2599,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=141,g(6987,1660,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=142,g(6987,2620,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=143,g(6987,1683,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=144,g(6987,2578,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=145,g(6987,1637,2,4744,4356,136,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=146,g(6987,2319,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=147,g(6987,2102,3,4760,4772,148,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=149,g(6987,1231,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=150,g(6987,1147,2,4724,4356,125,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=151,g(6987,2729,3,4732,4448,127,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=152,g(6987,2194,3,4780,4792,153,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=154,g(6987,2985,4,4800,4560,155,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=156,g(6987,3006,3,4816,4600,157,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=158,g(6987,1292,2,4828,4356,159,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=160,g(6987,1387,2,4836,4356,161,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=162,g(6987,2997,3,4844,4448,163,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=164,g(6987,2913,2,4856,4356,165,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=166,g(6987,2933,3,4864,4600,167,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=168,g(6987,3308,3,4876,4600,169,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=170,g(6987,3306,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=171,g(6987,3325,3,4888,4600,172,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=173,g(6987,3323,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=174,g(6987,1118,2,4652,4344,79,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=175,g(6987,1110,2,4900,4356,176,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=177,g(6987,1243,5,4912,4932,178,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=179,g(6987,1736,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=180,g(6987,1714,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=181,g(6987,2148,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=182,g(6987,2461,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=183,g(6987,2632,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=184,g(6987,1696,2,4752,4630,138,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=185,g(6987,1259,2,4940,4356,186,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=187,g(6987,2432,3,4760,4772,148,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=188,g(6987,2112,3,4760,4772,148,0|r,0),l[(r=eo(8))+4>>2]=0,l[r>>2]=189,g(6987,2740,3,4760,4772,148,0|r,0)}function rf(r,e,a,i,n,f,t){var b=0,o=0,k=0,c=0,u=0,h=0,v=0,p=0,w=0,y=0,E=0,G=0,R=0,g=0,Z=0,W=0,V=0,B=0,T=0,I=0;re=k=re-80|0,l[k+76>>2]=e,B=k+55|0,R=k+56|0;r:{e:{a:{i:{n:for(;;){if(c=e,(2147483647^w)<(0|b))break i;w=b+w|0;f:{t:{b:{if(o=d[0|(b=c)])for(;;){o:{e=255&o;k:{if(!e){e=b;break k}if((0|e)!=37)break o;for(o=b;;){if(37!=d[o+1|0]){e=o;break k}if(b=b+1|0,u=d[o+2|0],o=e=o+2|0,(0|u)!=37)break}}if((0|(b=b-c|0))>(0|(V=2147483647^w)))break i;if(r&&rF(r,c,b),b)continue n;l[k+76>>2]=e,b=e+1|0,E=-1,!eO(o=s[e+1|0])|36!=d[e+2|0]||(E=o-48|0,g=1,b=e+3|0),l[k+76>>2]=b,p=0,e=(o=s[0|b])-32|0;c:{if(e>>>0>31){u=b;break c}if(u=b,!(75913&(e=1<<e)))break c;for(;;){if(u=b+1|0,l[k+76>>2]=u,p|=e,(e=(o=s[b+1|0])-32|0)>>>0>=32)break c;if(b=u,!(75913&(e=1<<e)))break}}u:{if((0|o)==42){e=s[u+1|0];s:{if(!(!eO(e)|36!=d[u+2|0])){l[((e<<2)+n|0)-192>>2]=10,o=u+3|0,g=1,y=l[((s[u+1|0]<<3)+i|0)-384>>2];break s}if(g)break b;if(o=u+1|0,!r){l[k+76>>2]=o,g=0,y=0;break u}e=l[a>>2],l[a>>2]=e+4,g=0,y=l[e>>2]}if(l[k+76>>2]=o,(0|y)>=0)break u;y=0-y|0,p|=8192;break u}if((0|(y=ei(k+76|0)))<0)break i;o=l[k+76>>2]}b=0,v=-1;A:{if(46!=d[0|o]){e=o,G=0;break A}if(42==d[o+1|0]){e=s[o+2|0];l:{if(!(!eO(e)|36!=d[o+3|0])){l[((e<<2)+n|0)-192>>2]=10,e=o+4|0,v=l[((s[o+2|0]<<3)+i|0)-384>>2];break l}if(g)break b;if(e=o+2|0,v=0,!r)break l;o=l[a>>2],l[a>>2]=o+4,v=l[o>>2]}l[k+76>>2]=e,G=(-1^v)>>>31|0;break A}l[k+76>>2]=o+1,v=ei(k+76|0),e=l[k+76>>2],G=1}for(;;){if(h=b,u=28,Z=e,(b=s[0|e])-123>>>0<4294967238)break a;if(e=Z+1|0,!((b=d[(b+N(h,58)|0)+4895|0])-1>>>0<8))break}l[k+76>>2]=e;d:{h:{if((0|b)!=27){if(!b)break a;if((0|E)>=0){l[(E<<2)+n>>2]=b,o=l[(b=(E<<3)+i|0)+4>>2],l[k+64>>2]=l[b>>2],l[k+68>>2]=o;break h}if(!r)break f;rN(k- -64|0,b,a,t);break d}if((0|E)>=0)break a}if(b=0,!r)continue n}o=-65537&p,p=8192&p?o:p,E=0,W=1166,u=R;v:{p:{m:{N:{w:{y:{E:{G:{R:{g:{Z:{W:{V:{B:{T:{I:switch(b=s[0|Z],(b=h&&(15&b)==3?-33&b:b)-88|0){case 11:break v;case 9:case 13:case 14:case 15:break p;case 27:break E;case 12:case 17:break g;case 23:break Z;case 0:case 32:break W;case 24:break V;case 22:break B;case 29:break T;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 10:case 16:case 18:case 19:case 20:case 21:case 25:case 26:case 28:case 30:case 31:break t;default:break I}X:switch(b-65|0){case 0:case 4:case 5:case 6:break p;case 2:break w;case 1:case 3:break t;default:break X}if((0|b)==83)break y;break t}o=l[k+64>>2],h=l[k+68>>2],b=1166;break R}b=0;O:switch(255&h){case 0:case 1:case 6:l[l[k+64>>2]>>2]=w;continue n;case 2:c=l[k+64>>2],l[c>>2]=w,l[c+4>>2]=w>>31;continue n;case 3:A[l[k+64>>2]>>1]=w;continue n;case 4:s[l[k+64>>2]]=w;continue n;case 7:break O;default:continue n}c=l[k+64>>2],l[c>>2]=w,l[c+4>>2]=w>>31;continue n}v=v>>>0<=8?8:v,p|=8,b=120}if(c=R,o=l[k+64>>2],h=l[k+68>>2],o|h)for(T=32&b;s[0|(c=c-1|0)]=T|d[(15&o)+5424|0],I=!h&o>>>0>15|(0|h)!=0,Z=h,h=h>>>4|0,o=(15&Z)<<28|o>>>4,I;);if(!(8&p)|!(l[k+64>>2]|l[k+68>>2]))break G;W=(b>>>4|0)+1166|0,E=2;break G}if(b=R,h=c=l[k+68>>2],o=l[k+64>>2],c|o)for(;s[0|(b=b-1|0)]=7&o|48,Z=!h&o>>>0>7|(0|h)!=0,h=(c=h)>>>3|0,o=(7&c)<<29|o>>>3,Z;);if(c=b,!(8&p))break G;v=(0|(b=R-c|0))<(0|v)?v:b+1|0;break G}if(o=l[k+64>>2],h=b=l[k+68>>2],(0|b)<0){h=c=0-(h+((0|o)!=0)|0)|0,o=0-o|0,l[k+64>>2]=o,l[k+68>>2]=c,E=1,b=1166;break R}if(2048&p){E=1,b=1167;break R}b=(E=1&p)?1168:1166}W=b,c=rK(o,h,R)}if((0|v)<0?G:0)break i;if(p=G?-65537&p:p,b=l[k+64>>2],o=l[k+68>>2],!((b|o)!=0|v)){c=R,v=0;break t}v=(0|(b=!(b|o)+(R-c|0)|0))<(0|v)?v:b;break t}p=0,u=(0|(h=G=v>>>0>=2147483647?2147483647:v))!=0;L:{_:{b=c=(b=l[k+64>>2])||4208;F:{C:{S:if(!(!(3&b)|!h))for(;;){if(!(p=d[0|b]))break C;if(u=(0|(h=h-1|0))!=0,!(3&(b=b+1|0)))break S;if(!h)break}if(!u)break _;P:{if(!(!d[0|b]|h>>>0<4))for(;;){if((-1^(u=l[b>>2]))&u-16843009&-2139062144)break P;if(b=b+4|0,!((h=h-4|0)>>>0>3))break}if(!h)break _}u=0;break F}u=1}for(;;){if(!u){p=d[0|b],u=1;continue}if(!p)break L;if(b=b+1|0,!(h=h-1|0))break _;u=0}}b=0}if(u=(b=b?b-c|0:G)+c|0,(0|v)>=0){p=o,v=b;break t}if(p=o,v=b,d[0|u])break i;break t}if(v){o=l[k+64>>2];break N}b=0,er(r,32,y,0,p);break m}l[k+12>>2]=0,l[k+8>>2]=l[k+64>>2],o=k+8|0,l[k+64>>2]=o,v=-1}b=0;Y:{for(;;){if(!(c=l[o>>2]))break Y;if(!((u=(0|(c=el(k+4|0,c)))<0)|c>>>0>v-b>>>0)){if(o=o+4|0,v>>>0>(b=b+c|0)>>>0)continue;break Y}break}if(u)break e}if(u=61,(0|b)<0)break a;if(er(r,32,y,b,p),!b){b=0;break m}for(u=0,o=l[k+64>>2];;){if(!(c=l[o>>2])||(u=(c=el(k+4|0,c))+u|0)>>>0>b>>>0)break m;if(rF(r,k+4|0,c),o=o+4|0,!(b>>>0>u>>>0))break}}er(r,32,y,b,8192^p),b=(0|b)<(0|y)?y:b;continue n}if((0|v)<0?G:0)break i;if(u=61,(0|(b=0|eY[0|f](r,m[k+64>>3],y,v,p,b)))>=0)continue n;break a}s[k+55|0]=l[k+64>>2],v=1,c=B,p=o;break t}o=d[b+1|0],b=b+1|0}if(r)break r;if(!g)break f;for(b=1;;){if(r=l[(b<<2)+n>>2]){if(rN((b<<3)+i|0,r,a,t),w=1,(0|(b=b+1|0))!=10)continue;break r}break}if(w=1,b>>>0>=10)break r;for(;;){if(l[(b<<2)+n>>2])break b;if((0|(b=b+1|0))==10)break}break r}u=28;break a}if((0|(v=(0|v)>(0|(h=u-c|0))?v:h))>(2147483647^E))break i;if(u=61,(0|V)<(0|(b=(0|(o=v+E|0))<(0|y)?y:o)))break a;er(r,32,b,o,p),rF(r,W,E),er(r,48,b,o,65536^p),er(r,48,v,h,0),rF(r,c,h),er(r,32,b,o,8192^p);continue}break}w=0;break r}u=61}l[1761]=u}w=-1}return re=k+80|0,w}function rt(r,e,a,i,n,f,t,b,o,k,c,u,A,h){var m=w(0),E=0,G=0,R=0,g=w(0),Z=0,W=0,V=0,B=w(0);r:((l[r+312>>2]!=(0|h)?4&d[r+4|0]:0)||(Z=0,l[r+316>>2]!=(0|i)))&&(l[r+540>>2]=-1082130432,l[r+544>>2]=-1082130432,l[r+532>>2]=0,l[r+536>>2]=0,l[r+524>>2]=-1082130432,l[r+528>>2]=-1082130432,l[r+320>>2]=0,Z=1);W=A+1|0;e:{a:{i:{n:{if(l[r+8>>2]){if(g=rP(r,2,t),B=rS(r,2,t),m=w(rP(r,0,t)+rS(r,0,t)),A=r+524|0,g=w(g+B),rA(n,e,f,a,l[r+532>>2],p[A>>2],l[r+536>>2],p[r+528>>2],p[r+540>>2],p[r+544>>2],g,m,c))break i;if(!(G=l[r+320>>2]))break n;for(E=r+324|0;;){if(rA(n,e,f,a,l[(A=E+N(R,24)|0)+8>>2],p[A>>2],l[A+12>>2],p[A+4>>2],p[A+16>>2],p[A+20>>2],g,m,c))break i;if((0|G)==(0|(R=R+1|0)))break}break n}if(!o){if(!(V=l[r+320>>2]))break n;for(G=r+324|0;;){m=p[(A=(E=N(R,24))+G|0)>>2];f:{t:{if(!(m!=m|e!=e)){if(w(y(w(m-e)))<w(9999999747378752e-20))break t;break f}if(m==m|e==e)break f}m=p[(E=G+E|0)+4>>2];b:{if(!(m!=m|a!=a)){if(w(y(w(m-a)))<w(9999999747378752e-20))break b;break f}if(m==m|a==a)break f}if(l[E+8>>2]!=(0|n))break f;if(l[E+12>>2]==(0|f))break i}if((0|V)==(0|(R=R+1|0)))break}break n}m=p[(A=r+524|0)>>2];o:{if(!(m!=m|e!=e)){if(w(y(w(m-e)))<w(9999999747378752e-20))break o;break n}if(m==m|e==e)break n}E=l[r+532>>2]==(0|n)&&l[r+536>>2]==(0|f)?A:0,A=(m=p[r+528>>2])!=m,G=a!=a;k:{if(A|G){A&=G;break k}A=w(y(w(m-a)))<w(9999999747378752e-20)}A=A?E:0;break i}ri(r,e,a,i,n,f,t,b,o,c,u,W,h,k),l[r+316>>2]=i;break a}if(!(!A|Z)){p[r+516>>2]=p[A+16>>2],p[r+520>>2]=p[A+20>>2],l[(i=(o?12:16)+u|0)>>2]=l[i>>2]+1,i=0;break e}if(ri(r,e,a,i,n,f,t,b,o,c,u,W,h,k),l[r+316>>2]=i,i=1,A)break e}(i=(A=l[r+320>>2])+1|0)>>>0>v[u+8>>2]&&(l[u+8>>2]=i),(0|A)==8&&(l[r+320>>2]=0,A=0),o?i=r+524|0:(l[r+320>>2]=A+1,i=(N(A,24)+r|0)+324|0),l[i+12>>2]=f,l[i+8>>2]=n,p[i+4>>2]=a,p[i>>2]=e,p[i+16>>2]=p[r+516>>2],p[i+20>>2]=p[r+520>>2],i=1}c:if(o){if(n=l[r+520>>2],l[r+244>>2]=l[r+516>>2],l[r+248>>2]=n,f=1|(n=d[r+4|0]),s[r+4|0]=f,!(4&n))break c;s[r+4|0]=251&f}return l[r+312>>2]=h,i}function rb(r){r|=0;var e=0,a=0,i=0,n=0,f=0,t=0,b=0,o=0,k=0,c=0,u=0;b=8;r:if(!(r>>>0>4294967239)){e:{for(;;){b=b>>>0<=8?8:b,f=e=l[1731],n=l[1730],r=r>>>0<=8?8:r+3&-4;a:{if(r>>>0<=127){t=(r>>>3|0)-1|0;break a}if(i=E(r),t=((r>>>29-i^4)-(i<<2)|0)+110|0,r>>>0<=4095)break a;t=(i=((r>>>30-i^2)-(i<<1)|0)+71|0)>>>0>=63?63:i}if(a=31&t,(63&t)>>>0>=32?(i=0,e=e>>>a|0):(i=e>>>a|0,e=((1<<a)-1&e)<<32-a|n>>>a),e|i){for(;;){n=i;i:{if(e|i){o=(a=i-1|0)+1|0,f=a,f=(0|(a=e-1|0))!=-1?o:f,a=63-(i=(0|(i=E(i^f)))==32?E(e^a)+32|0:i)|0,ra=0-(i>>>0>63)|0;break i}ra=0,a=64}a=31&(f=a),(63&f)>>>0>=32?(i=0,e=n>>>a|0):(i=n>>>a|0,e=((1<<a)-1&n)<<32-a|e>>>a),o=e,e=l[(a=(t=f+t|0)<<4)+5896>>2],f=a+5888|0;n:{if((0|e)!=(0|f)){if(n=rp(e,b,r))break r;n=l[e+4>>2],l[n+8>>2]=l[e+8>>2],l[l[e+8>>2]+4>>2]=n,l[e+8>>2]=f,n=a+5892|0,l[e+4>>2]=l[n>>2],l[n>>2]=e,l[l[e+4>>2]+8>>2]=e,t=t+1|0,e=(1&i)<<31|o>>>1,i=i>>>1|0;break n}k=l[1731],n=31&(e=f=63&t),e>>>0>=32?(e=0,a=-1>>>n|0):a=(e=-1>>>n|0)|(1<<n)-1<<32-n,a&=-2,n=31&f,f>>>0>=32?(e=a<<n,a=0):(e=(1<<n)-1&a>>>32-n|e<<n,a<<=n),c=a,n=e,e=31&(a=f=0-t&63),a>>>0>=32?(e=-1<<e,a=0):e=(a=-1<<e)|(1<<e)-1&-1>>>32-e,u=-2&a,a=31&f,f>>>0>=32?(f=0,e=e>>>a|0):(f=e>>>a|0,e=((1<<a)-1&e)<<32-a|u>>>a),e|=c,ra=n|f,l[1730]=l[1730]&e,l[1731]=ra&k,e=1^o}if(!(e|i))break}f=l[1731],n=l[1730]}f:{if(n|f){i=l[(e=(a=63-((0|(i=E(f)))==32?E(n)+32|0:i)|0)<<4)+5896>>2];t:if(!(!f&n>>>0<1073741824)){if(t=99,(0|(e=e+5888|0))==(0|i))break t;for(;;){if(!t)break t;if(n=rp(i,b,r))break r;if(t=t-1|0,i=l[i+8>>2],(0|e)==(0|i))break}i=e}if(rd(r+48|0))break f;if(!i||(0|(e=(a<<4)+5888|0))==(0|i))break e;for(;;){if(n=rp(i,b,r))break r;if(i=l[i+8>>2],(0|e)==(0|i))break}break e}if(!rd(r+48|0))break e}if(n=0,b-1&b)break r;if(!(r>>>0<=4294967239))break}break r}n=0}return 0|n}function ro(r,e,a,i){var n,f,b,o,k=w(0),c=w(0),s=0,A=w(0);r:{e:{a:{i:{n:{f:{if(e>>>0<=1){c=p[r+212>>2],e=l[r+212>>2];t:if((0|e)!=2139156720){if((0|e)==2140081935)break f;s=4276;b:{if((0|e)!=2141891242){if(c==c)break b;s=4268}switch(k=p[s>>2],c=w(NaN),l[s+4>>2]-1|0){case 0:break f;case 1:break t;default:break n}}if(n=(-1073741825&e)+536870912|0,t[2]=n,k=u(),!(1073741824&e))break f}c=w(w(k*i)*w(.009999999776482582));break n}c=p[r+208>>2];o:{k:{e=l[r+208>>2];c:if((0|e)!=2139156720){if((0|e)==2140081935)break k;s=4276;u:{if((0|e)!=2141891242){if(c==c)break u;s=4268}switch(k=p[s>>2],c=w(NaN),l[s+4>>2]-1|0){case 0:break k;case 1:break c;default:break o}}if(f=(-1073741825&e)+536870912|0,t[2]=f,k=u(),!(1073741824&e))break k}c=w(w(k*i)*w(.009999999776482582));break o}c=k}k=w(0),A=p[r+216>>2],r=l[r+216>>2];s:if((0|r)!=2139156720){if((0|r)==2140081935)break a;e=4276;A:{if((0|r)!=2141891242){if(A==A)break A;e=4268}switch(k=p[e>>2],l[e+4>>2]-1|0){case 1:break s;case 0:break i;default:break e}}if(b=(-1073741825&r)+536870912|0,t[2]=b,k=u(),!(1073741824&r))break i}k=w(w(k*i)*w(.009999999776482582));break i}c=k}k=w(0),A=p[r+220>>2],r=l[r+220>>2];l:if((0|r)!=2139156720){if((0|r)==2140081935)break a;e=4276;d:{if((0|r)!=2141891242){if(A==A)break d;e=4268}switch(k=p[e>>2],l[e+4>>2]-1|0){case 1:break l;case 0:break i;default:break e}}if(o=(-1073741825&r)+536870912|0,t[2]=o,k=u(),!(1073741824&r))break i}k=w(w(k*i)*w(.009999999776482582))}if(!(k>=w(0)))break e}if(a>k)break r}if(!(c>=w(0)))return a;k=a<c?c:a}return k}function rk(r,e){var a=0,i=0;for(l[r+8>>2]=0,l[r+12>>2]=0,A[r+4>>1]=1,l[r>>2]=0,l[r+16>>2]=0,l[r+20>>2]=0,l[r+24>>2]=0,l[r+36>>2]=2143289344,l[r+40>>2]=2141891242,l[r+28>>2]=2143289344,l[r+32>>2]=2143289344,i=ry(r+44|0,0,36);l[i+(a<<2)>>2]=2143289344,(0|(a=a+1|0))!=9;);for(a=0,i=ry(r+80|0,0,36);l[i+(a<<2)>>2]=2143289344,(0|(a=a+1|0))!=9;);for(a=0,i=ry(r+116|0,0,36);l[i+(a<<2)>>2]=2143289344,(0|(a=a+1|0))!=9;);for(a=0,i=ry(r+152|0,0,36);l[i+(a<<2)>>2]=2143289344,(0|(a=a+1|0))!=9;);for(a=0,l[r+196>>2]=0,l[(i=r+188|0)>>2]=0,l[i+4>>2]=0;l[i+(a<<2)>>2]=2143289344,(0|(a=a+1|0))!=3;);for(l[r+24>>2]=4224,l[r+224>>2]=2143289344,l[r+216>>2]=2143289344,l[r+220>>2]=2143289344,l[r+208>>2]=2143289344,l[r+212>>2]=2143289344,l[r+200>>2]=2141891242,l[r+204>>2]=2141891242,ry(r+228|0,0,288),l[r+244>>2]=2143289344,l[r+248>>2]=2143289344,ry(r+252|0,0,49),l[r+308>>2]=2143289344,i=r+516|0,a=r+324|0;l[a+16>>2]=-1082130432,l[a+20>>2]=-1082130432,l[a+8>>2]=0,l[a+12>>2]=0,l[a>>2]=-1082130432,l[a+4>>2]=-1082130432,(0|i)!=(0|(a=a+24|0)););return l[r+548>>2]=0,l[r+552>>2]=0,l[r+516>>2]=2143289344,l[r+520>>2]=2143289344,l[r+572>>2]=2143289344,l[r+576>>2]=0,l[r+568>>2]=e,l[r+540>>2]=-1082130432,l[r+544>>2]=-1082130432,l[r+532>>2]=0,l[r+536>>2]=0,l[r+524>>2]=-1082130432,l[r+528>>2]=-1082130432,l[r+556>>2]=0,l[r+560>>2]=0,l[r+564>>2]=0,l[r+580>>2]=2143289344,l[r+584>>2]=0,d[e+10|0]&&(l[r+24>>2]=4616,s[r+4|0]=128|d[r+4|0]),r}function rc(r){var e,a,i=0,n=0,f=w(0),b=w(0),o=w(0),k=0,c=0,s=0,A=0,d=0,h=0,v=0,m=0,N=0;for(l[(k=re-16|0)+8>>2]=0,l[k+12>>2]=1,d=r+200|0,h=r+572|0,v=r+208|0,m=r+216|0,r=0;;){N=r,c=(A=l[(k+8|0)+(r<<2)>>2])<<2,f=p[(r=m+c|0)>>2];r:{e:{a:{i:{n:{f:if(!(!((s=(0|(i=l[r>>2]))==2139156720)|(0|i)==2140081935|(0|i)==2141891242)&f!=f)){b=p[(r=c+v|0)>>2],n=l[r>>2];t:{b:{if((0|n)!=2139156720){if(o=w(NaN),r=3,(0|n)==2141891242)break t;if((0|n)!=2140081935)break b;o=w(0),r=1;break t}o=w(0),r=2;break t}if(r=0,b!=b)break t;e=(-1073741825&n)+536870912|0,t[2]=e,o=u(),r=1073741824&n?2:1}n=r;o:{k:{if(!s){if(b=w(NaN),r=3,(0|i)==2141891242)break o;if((0|i)!=2140081935)break k;b=w(0),r=1;break o}b=w(0),r=2;break o}if(r=0,f!=f)break o;a=(-1073741825&i)+536870912|0,t[2]=a,b=u(),r=1073741824&i?2:1}if((0|n)!=(0|r)|!(!r|b!=b&o!=o|w(y(w(b-o)))<w(9999999747378752e-20)))break f;if(r=0,s)break n;if((0|i)==2140081935)break e;if((0|i)==2141891242)break a;if(f!=f)break i;r=(-1073741825&i)+536870912|0,i=1073741824&i?2:1;break r}if(r=0,f=p[(i=c+d|0)>>2],(0|(i=l[i>>2]))==2139156720)break n;if((0|i)==2140081935)break e;if((0|i)==2141891242)break a;if(f!=f)break i;r=(-1073741825&i)+536870912|0,i=1073741824&i?2:1;break r}i=2;break r}r=2143289344,i=0;break r}r=2143289344,i=3;break r}i=1}if(l[(n=(A<<3)+h|0)+4>>2]=i,l[n>>2]=r,r=1,1&N)break}}function ru(r,e,a){var i=0,n=0,f=0;if(a>>>0>=512)return x(0|r,0|e,0|a),r;n=r+a|0;r:{if(!((r^e)&3)){e:{if(!(3&r)||!a){a=r;break e}for(a=r;;){if(s[0|a]=d[0|e],e=e+1|0,!(3&(a=a+1|0)))break e;if(!(a>>>0<n>>>0))break}}i=-4&n;a:if(!(i>>>0<64)&&!((f=i+-64|0)>>>0<a>>>0))for(;l[a>>2]=l[e>>2],l[a+4>>2]=l[e+4>>2],l[a+8>>2]=l[e+8>>2],l[a+12>>2]=l[e+12>>2],l[a+16>>2]=l[e+16>>2],l[a+20>>2]=l[e+20>>2],l[a+24>>2]=l[e+24>>2],l[a+28>>2]=l[e+28>>2],l[a+32>>2]=l[e+32>>2],l[a+36>>2]=l[e+36>>2],l[a+40>>2]=l[e+40>>2],l[a+44>>2]=l[e+44>>2],l[a+48>>2]=l[e+48>>2],l[a+52>>2]=l[e+52>>2],l[a+56>>2]=l[e+56>>2],l[a+60>>2]=l[e+60>>2],e=e- -64|0,f>>>0>=(a=a- -64|0)>>>0;);if(a>>>0>=i>>>0)break r;for(;l[a>>2]=l[e>>2],e=e+4|0,i>>>0>(a=a+4|0)>>>0;);break r}if(n>>>0<4||(i=n-4|0)>>>0<r>>>0){a=r;break r}for(a=r;s[0|a]=d[0|e],s[a+1|0]=d[e+1|0],s[a+2|0]=d[e+2|0],s[a+3|0]=d[e+3|0],e=e+4|0,i>>>0>=(a=a+4|0)>>>0;);}if(a>>>0<n>>>0)for(;s[0|a]=d[0|e],e=e+1|0,(0|n)!=(0|(a=a+1|0)););return r}function rs(r,e,a,i,n){var f,b,o=w(0),k=0,c=w(0),s=w(0),A=0,d=0,h=w(0),v=w(0),m=0;if(!(d=(c=w(a-i))!=c)){a=w(0),o=p[(k=(e<<2)+r|0)+208>>2];r:{e:{k=l[k+208>>2];a:{i:if((0|k)!=2139156720){if((0|k)==2140081935)break e;A=4276;n:{if((0|k)!=2141891242){if(o==o)break n;A=4268}switch(a=p[A>>2],l[A+4>>2]-1|0){case 0:break a;case 1:break i;default:break r}}if(f=(-1073741825&k)+536870912|0,t[2]=f,a=u(),!(1073741824&k))break a}a=w(w(a*n)*w(.009999999776482582))}if(a==a)break e;break r}s=w(a-i)}a=w(0),o=p[(r=(e<<2)+r|0)+216>>2];f:{t:{e=l[r+216>>2];b:{o:if((0|e)!=2139156720){if((0|e)==2140081935)break t;r=4276;k:{if((0|e)!=2141891242){if(o==o)break k;r=4268}switch(a=p[r>>2],o=w(34028234663852886e22),l[r+4>>2]-1|0){case 0:break b;case 1:break o;default:break f}}if(b=(-1073741825&e)+536870912|0,t[2]=b,a=u(),!(1073741824&e))break b}a=w(w(a*n)*w(.009999999776482582))}if(a==a)break t;o=w(34028234663852886e22);break f}o=w(a-i)}if(h=c,v=es(c,o),!((r=(a=o!=o|d?h:v)!=a)|s!=s))return eu(a,s);c=r?s:a}return c}function rA(r,e,a,i,n,f,t,b,o,k,c,u,s){var A=w(0),l=w(0),d=0,h=0,v=w(0),m=w(0),N=w(0),E=0,G=0;if(o<w(0)|k<w(0))r=0;else{v=f,m=e,N=i,l=b;r:if(s){if(A=p[s+16>>2],l=b,A==w(0))break r;m=rH(+e,E=+A,0,0),N=rH(+i,E,0,0),v=rH(+f,E,0,0),l=rH(+b,E,0,0)}A=l,s=0;e:if((0|r)==(0|n)){if((G=v!=v)|(s=m!=m)){s&=G;break e}s=w(y(w(v-m)))<w(9999999747378752e-20)}G=s;a:if((0|a)==(0|t)){if((d=A!=A)|(s=N!=N)){d&=s;break a}d=w(y(w(A-N)))<w(9999999747378752e-20)}h=1,s=1;i:if(!G){e=w(e-c);n:if((0|r)!=1){if(!((r=(0|r)!=2)|n)){if(!(e>=o))break n;break i}if(s=0,!(e<f)|(r|(0|n)!=2|(e!=e|f!=f|o!=o))||(s=1,e>=o))break i}if((n=e!=e)|(r=o!=o)){s=r&n;break i}s=w(y(w(e-o)))<w(9999999747378752e-20)}f:if(!d){e=w(i-u);t:if((0|a)!=1){if(!((r=(0|a)!=2)|t)){if(!(e>=k))break t;break f}if(h=0,!(e<b)|(r|(0|t)!=2|(e!=e|b!=b|k!=k))||(h=1,e>=k))break f}if((a=e!=e)|(r=k!=k)){h=r&a;break f}h=w(y(w(e-k)))<w(9999999747378752e-20)}r=s&h}return r}function rl(r){var e,a,i,n=0,f=0,b=0,k=0,c=0,u=0,s=0,A=0;if(e=+r,o[0]=e,c=0|t[1],b=0|t[0],(0|(k=c>>>20&2047))==2047)return(r*=1)/r;if(!(n=b<<1)&(0|(u=c<<1|b>>>31))==2145386496|u>>>0<2145386496)return!n&(0|u)==2145386496?0*r:r;r:{if(!k){if(k=0,f=b<<12,(0|(n=c<<12|b>>>20))>0|(0|n)>=0)for(;k=k-1|0,n=n<<1|f>>>31,f<<=1,(0|n)>0|(0|n)>=0;);f=31&(n=1-k|0),(63&n)>>>0>=32?(n=b<<f,b=0):(n=(1<<f)-1&b>>>32-f|c<<f,b<<=f);break r}n=1048575&c|1048576}if(f=b,(0|k)>1023){for(;;){e:if(!((0|(b=n+-1048576|0))<0)&&!((n=b)|f))return 0*r;if(n=n<<1|f>>>31,f<<=1,!((0|(k=k-1|0))>1023))break}k=1023}a:if(!((0|(b=n+-1048576|0))<0)&&!((n=b)|f))return 0*r;if((0|n)==1048575|n>>>0<1048575)for(;k=k-1|0,b=n>>>0<524288,u=n<<1|f>>>31,f<<=1,n=u,b;);return A=-2147483648&c,u=n+-1048576|k<<20,s=f,c=n,s=f,f=31&(b=1-k|0),(63&b)>>>0>=32?(n=0,b=c>>>f|0):(n=c>>>f|0,b=((1<<f)-1&c)<<32-f|s>>>f),a=((f=(0|k)>0)?s:b)|0,t[0]=a,i=(f?u:n)|A,t[1]=i,+o[0]}function rd(r){var e=0,a=0,i=0,n=0,f=0;a=(i=l[1396])+(e=r+7&-8)|0;r:{e:if(!((a>>>0<=i>>>0?e:0)||a>>>0>eU()<<16>>>0&&!(0|D(0|a)))){l[1396]=a;break r}l[1761]=48,i=-1}if((0|i)!=-1){l[(a=(e=r+i|0)-16|0)+12>>2]=16,l[a>>2]=16,f=(r=l[1728])?l[r+8>>2]:0;a:{i:{if((0|f)==(0|i)){if(n=i-(-2&l[i-4>>2])|0,f=l[n-4>>2],l[r+8>>2]=e,e=-16,!(1&s[((r=n-(-2&f)|0)+l[r>>2]|0)-4|0]))break i;e=l[r+4>>2],l[e+8>>2]=l[r+8>>2],l[l[r+8>>2]+4>>2]=e,a=a-r|0,l[r>>2]=a;break a}l[i+12>>2]=16,l[i>>2]=16,l[i+8>>2]=e,l[i+4>>2]=r,l[1728]=i,e=16}a=a-(r=e+i|0)|0,l[r>>2]=a}l[((-4&a)+r|0)-4>>2]=1|a,e=l[r>>2]-8|0;n:{if(e>>>0<=127){a=(e>>>3|0)-1|0;break n}if(n=E(e),a=((e>>>29-n^4)-(n<<2)|0)+110|0,e>>>0<=4095)break n;a=(a=((e>>>30-n^2)-(n<<1)|0)+71|0)>>>0>=63?63:a}e=a<<4,l[r+4>>2]=e+5888,e=e+5896|0,l[r+8>>2]=l[e>>2],l[e>>2]=r,l[l[r+8>>2]+4>>2]=r,e=l[1730],n=l[1731],r=31&a,(63&a)>>>0>=32?(a=1<<r,f=0):a=(f=1<<r)-1&1>>>32-r,l[1730]=f|e,l[1731]=a|n}return(0|i)!=-1}function rh(r,e,a){var i=0,n=0,f=0,t=0,b=0,o=0,k=0,c=0,u=0;r:{e:{a:{i:{n:{f:{t:{b:{o:{k:{if(e){if(!a)break k;break o}ra=0,r=(r>>>0)/(a>>>0)|0;break r}if(!r)break b;break t}if(!(a-1&a))break f;t=0-(f=(E(a)+33|0)-E(e)|0)|0;break i}ra=0,r=(e>>>0)/0|0;break r}if((i=32-E(e)|0)>>>0<31)break n;break a}if((0|a)==1)break e;a=31&(f=a?31-E(a-1^a)|0:32),(63&f)>>>0>=32?r=e>>>a|0:(i=e>>>a|0,r=((1<<a)-1&e)<<32-a|r>>>a),ra=i;break r}f=i+1|0,t=63-i|0}if(n=31&(i=63&f),i>>>0>=32?(i=0,b=e>>>n|0):(i=e>>>n|0,b=((1<<n)-1&e)<<32-n|r>>>n),t&=63,n=31&t,t>>>0>=32?(e=r<<n,r=0):(e=(1<<n)-1&r>>>32-n|e<<n,r<<=n),f)for(u=(0|(t=a-1|0))==-1?-1:0;k=a&(n=u-((o=i<<1|b>>>31)+((i=b<<1|e>>>31)>>>0>t>>>0)|0)>>31),b=i-k|0,i=o-(i>>>0<k>>>0)|0,e=e<<1|r>>>31,r=c|r<<1,c=1&n,f=f-1|0;);ra=e<<1|r>>>31,r=c|r<<1;break r}r=0,e=0}ra=e}return r}function rv(r,e,a){var i,n,f=w(0),b=w(0);r:{e:{a:{if((-2&e)==2){if(e=l[r+188>>2],f=p[r+188>>2],(0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|f==f||(e=l[r+196>>2],f=p[r+196>>2],(0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|f==f))break a;break e}if(e=l[r+192>>2],f=p[r+192>>2],(0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|f==f||(f=p[r+196>>2],(0|(e=l[r+196>>2]))==2139156720|(0|e)==2140081935|(0|e)==2141891242))break a;if(f!=f)break e}f=w(0);i:{n:if((0|e)!=2139156720){if((0|e)==2140081935)break r;r=4276;f:{if((0|e)!=2141891242){if(i=e,t[2]=i,(f=u())==f)break f;r=4268}switch(f=p[r>>2],b=w(NaN),l[r+4>>2]-1|0){case 0:break r;case 1:break n;default:break i}}if(n=(-1073741825&e)+536870912|0,t[2]=n,f=u(),!(1073741824&e))break r}b=w(w(f*a)*w(.009999999776482582))}return b}f=w(0)}return f}function rp(r,e,a){var i=0,n=0,f=0,t=0;if(n=((i=r+4|0)+e|0)-1&0-e,e=l[r>>2],n+a>>>0<=(e+r|0)-4>>>0){f=l[r+4>>2],l[f+8>>2]=l[r+8>>2],l[l[r+8>>2]+4>>2]=f,(0|i)!=(0|n)&&(n=n-i|0,f=r-(-2&l[r-4>>2])|0,i=n+l[f>>2]|0,l[f>>2]=i,l[(f+(-4&i)|0)-4>>2]=i,r=r+n|0,e=e-n|0,l[r>>2]=e);r:{if(a+24>>>0<=e>>>0){i=(r+a|0)+8|0,e=(e-a|0)-8|0,l[i>>2]=e,l[(i+(-4&e)|0)-4>>2]=1|e,n=l[i>>2]-8|0;e:{if(n>>>0<=127){e=(n>>>3|0)-1|0;break e}if(f=E(n),e=((n>>>29-f^4)-(f<<2)|0)+110|0,n>>>0<=4095)break e;e=(e=((n>>>30-f^2)-(f<<1)|0)+71|0)>>>0>=63?63:e}n=e<<4,l[i+4>>2]=n+5888,n=n+5896|0,l[i+8>>2]=l[n>>2],l[n>>2]=i,l[l[i+8>>2]+4>>2]=i,n=l[1730],f=l[1731],i=31&e,(63&e)>>>0>=32?(e=1<<i,i=0):(e=(t=1<<i)-1&1>>>32-i,i=t),l[1730]=i|n,l[1731]=e|f,e=a+8|0,l[r>>2]=e,l[((-4&e)+r|0)-4>>2]=e;break r}l[(r+e|0)-4>>2]=e}r=r+4|0}else r=0;return r}function rm(r,e,a){var i=0,n=0;r:if((0|r)!=(0|e)){if(e-(n=r+a|0)>>>0<=0-(a<<1)>>>0)return ru(r,e,a);i=(r^e)&3;e:{a:{if(r>>>0<e>>>0){if(i){i=r;break e}if(!(3&r)){i=r;break a}for(i=r;;){if(!a)break r;if(s[0|i]=d[0|e],e=e+1|0,a=a-1|0,!(3&(i=i+1|0)))break}break a}i:if(!i){if(3&n)for(;;){if(!a)break r;if(s[0|(i=(a=a-1|0)+r|0)]=d[e+a|0],!(3&i))break}if(a>>>0<=3)break i;for(;l[(a=a-4|0)+r>>2]=l[e+a>>2],a>>>0>3;);}if(!a)break r;for(;s[(a=a-1|0)+r|0]=d[e+a|0],a;);break r}if(a>>>0<=3)break e;for(;l[i>>2]=l[e>>2],e=e+4|0,i=i+4|0,(a=a-4|0)>>>0>3;);}if(!a)break r;for(;s[0|i]=d[0|e],i=i+1|0,e=e+1|0,a=a-1|0;);}return r}function rN(r,e,a,i){r:switch(e-9|0){case 0:e=l[a>>2],l[a>>2]=e+4,l[r>>2]=l[e>>2];return;case 6:e=l[a>>2],l[a>>2]=e+4,e=A[e>>1],l[r>>2]=e,l[r+4>>2]=e>>31;return;case 7:e=l[a>>2],l[a>>2]=e+4,l[r>>2]=h[e>>1],l[r+4>>2]=0;return;case 8:e=l[a>>2],l[a>>2]=e+4,e=s[0|e],l[r>>2]=e,l[r+4>>2]=e>>31;return;case 9:e=l[a>>2],l[a>>2]=e+4,l[r>>2]=d[0|e],l[r+4>>2]=0;return;case 16:e=l[a>>2]+7&-8,l[a>>2]=e+8,m[r>>3]=m[e>>3];return;case 17:eY[0|i](r,a);default:return;case 1:case 4:case 14:e=l[a>>2],l[a>>2]=e+4,e=l[e>>2],l[r>>2]=e,l[r+4>>2]=e>>31;return;case 2:case 5:case 11:case 15:e=l[a>>2],l[a>>2]=e+4,l[r>>2]=l[e>>2],l[r+4>>2]=0;return;case 3:case 10:case 12:case 13:break r}e=l[a>>2]+7&-8,l[a>>2]=e+8,a=l[e+4>>2],l[r>>2]=l[e>>2],l[r+4>>2]=a}function rw(){rr(7004,3018),$(7005,2479,1,1,0),V(7006,2139,1,-128,127),V(7007,2132,1,-128,127),V(7008,2130,1,0,255),V(7009,1286,2,-32768,32767),V(7010,1277,2,0,65535),V(7011,1315,4,-2147483648,2147483647),V(7012,1306,4,0,-1),V(7013,2658,4,-2147483648,2147483647),V(7014,2649,4,0,-1),eV(7015,1776,-2147483648,2147483647),eV(7016,1775,0,-1),C(7017,1769,4),C(7018,2966,8),S(7019,2676),S(7020,3835),O(7021,4,2663),O(7022,2,2688),O(7023,4,2703),q(7024,2484),Z(7025,0,3766),Z(7026,0,3868),Z(7027,1,3796),Z(7028,2,3398),Z(7029,3,3429),Z(7030,4,3469),Z(7031,5,3498),Z(7032,4,3905),Z(7033,5,3935),Z(7026,0,3600),Z(7027,1,3567),Z(7028,2,3666),Z(7029,3,3632),Z(7030,4,3733),Z(7031,5,3699),Z(7034,6,3536),Z(7035,7,3974)}function ry(r,e,a){var i=0,n=0,f=0,t=0;r:if(a){if(s[0|r]=e,s[(i=r+a|0)-1|0]=e,a>>>0<3||(s[r+2|0]=e,s[r+1|0]=e,s[i-3|0]=e,s[i-2|0]=e,a>>>0<7)||(s[r+3|0]=e,s[i-4|0]=e,a>>>0<9)||(n=(i=0-r&3)+r|0,e=N(255&e,16843009),l[n>>2]=e,l[(a=(i=a-i&-4)+n|0)-4>>2]=e,i>>>0<9)||(l[n+8>>2]=e,l[n+4>>2]=e,l[a-8>>2]=e,l[a-12>>2]=e,i>>>0<25)||(l[n+24>>2]=e,l[n+20>>2]=e,l[n+16>>2]=e,l[n+12>>2]=e,l[a-16>>2]=e,l[a-20>>2]=e,l[a-24>>2]=e,l[a-28>>2]=e,(a=i-(t=4&n|24)|0)>>>0<32))break r;for(i=r9(e,0,1,1),f=ra,e=n+t|0;l[e+24>>2]=i,l[e+28>>2]=f,l[e+16>>2]=i,l[e+20>>2]=f,l[e+8>>2]=i,l[e+12>>2]=f,l[e>>2]=i,l[e+4>>2]=f,e=e+32|0,(a=a-32|0)>>>0>31;);}return r}function rE(r){var e=0,a=0,i=0,n=0,f=0;if(r|=0){a=n=l[(e=r-4|0)>>2],i=e,(0|(r=-2&(f=l[r-8>>2])))!=(0|f)&&(a=l[(i=e-r|0)+4>>2],l[a+8>>2]=l[i+8>>2],l[l[i+8>>2]+4>>2]=a,a=r+n|0),(0|(e=l[(r=e+n|0)>>2]))!=l[(r+e|0)-4>>2]&&(n=l[r+4>>2],l[n+8>>2]=l[r+8>>2],l[l[r+8>>2]+4>>2]=n,a=e+a|0),l[i>>2]=a,l[((-4&a)+i|0)-4>>2]=1|a,e=l[i>>2]-8|0;r:{if(e>>>0<=127){r=(e>>>3|0)-1|0;break r}if(a=E(e),r=((e>>>29-a^4)-(a<<2)|0)+110|0,e>>>0<=4095)break r;r=(r=((e>>>30-a^2)-(a<<1)|0)+71|0)>>>0>=63?63:r}e=r<<4,l[i+4>>2]=e+5888,e=e+5896|0,l[i+8>>2]=l[e>>2],l[e>>2]=i,l[l[i+8>>2]+4>>2]=i,e=l[1730],a=l[1731],i=31&r,(63&r)>>>0>=32?(r=1<<i,n=0):r=(n=1<<i)-1&1>>>32-i,l[1730]=n|e,l[1731]=r|a}}function rG(r,e,a,i,n){var f=0,t=0,b=0,o=0;re=f=re-208|0,l[f+204>>2]=a,ry(a=f+160|0,0,40),l[f+200>>2]=l[f+204>>2];r:{if((0|rf(0,e,f+200|0,f+80|0,a,i,n))<0){n=-1;break r}o=l[r+76>>2]>=0,t=l[r>>2],l[r+72>>2]<=0&&(l[r>>2]=-33&t);e:{a:{i:{if(!l[r+48>>2]){l[r+48>>2]=80,l[r+28>>2]=0,l[r+16>>2]=0,l[r+20>>2]=0,b=l[r+44>>2],l[r+44>>2]=f;break i}if(l[r+16>>2])break a}if(a=-1,ef(r))break e}a=rf(r,e,f+200|0,f+80|0,f+160|0,i,n)}if(b&&(eY[l[r+36>>2]](r,0,0),l[r+48>>2]=0,l[r+44>>2]=b,l[r+28>>2]=0,e=l[r+20>>2],l[r+16>>2]=0,l[r+20>>2]=0,a=e?a:-1),e=r,r=l[r>>2],l[e>>2]=r|32&t,n=32&r?-1:a,!o)break r}return re=f+208|0,n}function rR(r){var e=0,a=w(0),i=0,n=0,f=0,t=0,b=0,o=w(0),c=0;re=f=re-16|0,e=l[r+12>>2];r:{if(e){o=p[r+520>>2],a=p[r+516>>2];e:{if(32&d[r+4|0]){a=w(eY[0|e](r,a,o,0));break e}a=w(eY[0|e](r,a,o))}if(a==a)break r;l[f>>2]=3340,rB(r,f),eX(),k()}a:{i:{if(t=l[r+556>>2],e=l[r+560>>2],(0|t)!=(0|e)){for(c=(e=e-t>>2)>>>0<=1?1:e;;){if(n=l[(b<<2)+t>>2],!l[n+548>>2]){if((196608&(e=l[n+24>>2]))!=131072){if((e=e>>>13&7)||(e=l[r+24>>2]>>>10&7),2&d[n+4|0]|(8&d[r+24|0]?(0|e)==5:0))break i;i=i||n}if((0|c)!=(0|(b=b+1|0)))continue}break}if(i)break a}a=p[r+520>>2];break r}i=n}a=w(rR(i)+p[i+232>>2])}return re=f+16|0,a}function rg(r,e,a,i,n,f){var b,o=0,k=w(0),c=0,s=w(0);k=p[(o=(l[(e<<2)+4252>>2]<<2)+r|0)+216>>2];r:{e:{o=l[o+216>>2];a:if((0|o)!=2139156720){if((0|o)==2140081935)break e;c=4276;i:{if((0|o)!=2141891242){if(k==k)break i;c=4268}switch(s=p[c>>2],k=w(NaN),l[c+4>>2]-1|0){case 0:break e;case 1:break a;default:break r}}if(b=(-1073741825&o)+536870912|0,t[2]=b,s=u(),!(1073741824&o))break e}k=w(w(s*a)*w(.009999999776482582));break r}k=s}i=w(k+w(rP(r,e,i)+rS(r,e,i)));n:{f:{t:switch(l[n>>2]){case 1:case 2:a=p[f>>2],i=i!=i?a:a<i?a:i;break f;case 0:break t;default:break n}if(i!=i)break n;l[n>>2]=2}p[f>>2]=i}}function rZ(r,e,a){var i,n,f=w(0),b=w(0),o=0,k=w(0);r=r+116|0,o=l[(e<<2)+4220>>2];r:{if((-2&e)==2){e=rX(r,4,o,2140081935);break r}e=rY(r,o,2140081935)}e:{a:{i:{n:if((0|e)!=2139156720){if((0|e)==2140081935)break e;r=4276;f:{if((0|e)!=2141891242){if(i=e,t[2]=i,(f=u())==f)break f;r=4268}switch(f=p[r>>2],b=w(NaN),l[r+4>>2]-1|0){case 0:break i;case 1:break n;default:break a}}if(n=(-1073741825&e)+536870912|0,t[2]=n,f=u(),!(1073741824&e))break i}f=w(w(f*a)*w(.009999999776482582))}if(f>=w(0))return f;if(b=f,f<w(0))break e}k=b==b?b:w(0)}return k}function rW(r,e,a){var i,n,f=w(0),b=w(0),o=0,k=w(0);r=r+116|0,o=l[(e<<2)+4236>>2];r:{if((-2&e)==2){e=rX(r,5,o,2140081935);break r}e=rY(r,o,2140081935)}e:{a:{i:{n:if((0|e)!=2139156720){if((0|e)==2140081935)break e;r=4276;f:{if((0|e)!=2141891242){if(i=e,t[2]=i,(f=u())==f)break f;r=4268}switch(f=p[r>>2],b=w(NaN),l[r+4>>2]-1|0){case 0:break i;case 1:break n;default:break a}}if(n=(-1073741825&e)+536870912|0,t[2]=n,f=u(),!(1073741824&e))break i}f=w(w(f*a)*w(.009999999776482582))}if(f>=w(0))return f;if(b=f,f<w(0))break e}k=b==b?b:w(0)}return k}function rV(r,e,a,i,n,f){var t=0;r:{e:{if(r){a:{if(255&e){f=0|eY[0|r](a,i,n,f);break a}f=0|eY[0|r](a,i,n)}if(f)break e}if(l[(e=ru(f=eo(588),a,552))+564>>2]=0,l[e+556>>2]=0,l[e+560>>2]=0,n=l[a+556>>2],i=l[a+560>>2],(0|n)!=(0|i)){if((0|(t=i-n|0))<0)break r;for(r=eo(t),l[e+556>>2]=r,l[e+564>>2]=r+t;l[r>>2]=l[n>>2],r=r+4|0,(0|i)!=(0|(n=n+4|0)););l[e+560>>2]=r}r=l[a+572>>2],l[e+568>>2]=l[a+568>>2],l[e+572>>2]=r,l[e+584>>2]=l[a+584>>2],r=l[a+580>>2],l[e+576>>2]=l[a+576>>2],l[e+580>>2]=r,l[e+552>>2]=0}return f}T(),k()}function rB(r,e){var a=0,i=0,n=0;re=n=re-16|0,l[n+12>>2]=e;r:{e:{if(!r){e=l[n+12>>2];break e}if(e=l[n+12>>2],!(a=l[r+568>>2]))break e;if(i=l[a+4>>2],d[a+9|0]){eY[0|i](a,r,5,0,4215,e);break r}eY[0|i](a,r,5,4215,e);break r}a:{if(!d[6936]){A[(a=eo(28))+20>>1]=0,l[a+16>>2]=1065353216,A[a+10>>1]=0,A[a+12>>1]=0,l[a>>2]=0,l[a+24>>2]=0,s[a+9|0]=0,i=1,l[a+4>>2]=1,s[a+22|0]=0,l[1733]=a,s[6936]=1,l[1732]=l[1732]+1;break a}if(a=l[1733],i=l[a+4>>2],!d[a+9|0])break a;eY[0|i](a,r,5,0,4215,e);break r}eY[0|i](a,r,5,4215,e)}re=n+16|0}function rT(r,e,a){var i,n,f=w(0),b=0,o=w(0);r=r+80|0,b=l[(e<<2)+4236>>2];r:{e:{if((-2&e)==2){e=rX(r,5,b,2140081935);break e}e=rY(r,b,2140081935)}a:{i:if((0|e)!=2139156720){if((0|e)==2140081935)break r;r=4276;n:{if((0|e)!=2141891242){if(i=e,t[2]=i,(f=u())==f)break n;r=4268}switch(f=p[r>>2],o=w(NaN),l[r+4>>2]-1|0){case 0:break r;case 1:break i;default:break a}}if(n=(-1073741825&e)+536870912|0,t[2]=n,f=u(),!(1073741824&e))break r}o=w(w(f*a)*w(.009999999776482582))}return o}return f}function rI(r,e,a){var i,n,f=w(0),b=0,o=w(0);r=r+80|0,b=l[(e<<2)+4220>>2];r:{e:{if((-2&e)==2){e=rX(r,4,b,2140081935);break e}e=rY(r,b,2140081935)}a:{i:if((0|e)!=2139156720){if((0|e)==2140081935)break r;r=4276;n:{if((0|e)!=2141891242){if(i=e,t[2]=i,(f=u())==f)break n;r=4268}switch(f=p[r>>2],o=w(NaN),l[r+4>>2]-1|0){case 0:break r;case 1:break i;default:break a}}if(n=(-1073741825&e)+536870912|0,t[2]=n,f=u(),!(1073741824&e))break r}o=w(w(f*a)*w(.009999999776482582))}return o}return f}function rX(r,e,a,i){var n=w(0);n=p[(e=(e<<2)+r|0)>>2],e=l[e>>2];r:if(!((0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|n==n)){if(n=p[(e=(a<<2)+r|0)>>2],(0|(e=l[e>>2]))==2139156720|(0|e)==2140081935|(0|e)==2141891242|n==n||(e=l[r+24>>2],n=p[r+24>>2],(0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|n==n)||(n=p[r+32>>2],!((0|(r=l[r+32>>2]))==2139156720|(0|r)==2140081935|(0|r)==2141891242)&&(e=i,n!=n)))break r;e=r}return e}function rO(r,e,a,i,n){var f=0,t=0,b=w(0),o=0;f=2,t=l[r+24>>2]>>>2&3;r:{e:{a:{if(!(!l[r+552>>2]|(0|e)!=2)){e=0,f=3;i:switch(t-2|0){case 0:break r;case 1:break i;default:break a}f=2;break r}if(e=0,t>>>0>1)break e}e=f}f=t}a=ey(r,f,a),i=ey(r,e,i),b=rP(r,f,n),p[(t=r+228|0)+(l[(o=f<<2)+4220>>2]<<2)>>2]=a+b,b=rS(r,f,n),p[t+(l[o+4236>>2]<<2)>>2]=a+b,a=rP(r,e,n),p[t+(l[(f=e<<2)+4220>>2]<<2)>>2]=i+a,a=rS(r,e,n),p[t+(l[f+4236>>2]<<2)>>2]=i+a}function rL(r){var e=0,a=0,i=0,n=0;if(r|=0){if(e=l[r>>2],(a=l[e+552>>2])&&(rq(a,e),l[e+552>>2]=0),i=l[e+560>>2],a=l[e+556>>2],(0|i)!=(0|a))for(n=(i=i-a>>2)>>>0<=1?1:i,i=0;l[l[a+(i<<2)>>2]+552>>2]=0,(0|n)!=(0|(i=i+1|0)););l[e+560>>2]=a,r_(e+556|0),(a=l[e+556>>2])&&(l[e+560>>2]=a,rE(a)),rE(e),e=l[r+8>>2],l[r+8>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e),e=l[r+4>>2],l[r+4>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e),rE(r)}}function r_(r){var e=0,a=0,i=0,n=0,f=0;re=n=re-32|0,e=l[r>>2],a=l[r+4>>2]-e>>2;r:if(!(a>>>0>=l[r+8>>2]-e>>2>>>0)){if(a=en(n+8|0,a,a,r+8|0),e=l[r>>2],i=l[r+4>>2]-e|0,i=rm(l[a+4>>2]-i|0,e,i),e=l[r>>2],l[r>>2]=i,l[a+4>>2]=e,i=l[r+4>>2],l[r+4>>2]=l[a+8>>2],l[a+8>>2]=i,f=l[r+8>>2],l[r+8>>2]=l[a+12>>2],l[a>>2]=e,l[a+12>>2]=f,(0|e)!=(0|i)&&(l[a+8>>2]=i+((e-i|0)+3&-4)),!e)break r;rE(e)}re=n+32|0}function rF(r,e,a){var i=0,n=0,f=0;if(!(32&d[0|r]))r:{i=e,r=l[(e=r)+16>>2];e:{if(!r){if(ef(e))break e;r=l[e+16>>2]}if(f=l[e+20>>2],r-f>>>0<a>>>0){eY[l[e+36>>2]](e,i,a);break r}a:if(!(l[e+80>>2]<0)){for(r=a;;){if(n=r,!r)break a;if(10==d[i+(r=n-1|0)|0])break}if(eY[l[e+36>>2]](e,i,n)>>>0<n>>>0)break e;i=i+n|0,a=a-n|0,f=l[e+20>>2]}ru(f,i,a),l[e+20>>2]=l[e+20>>2]+a}}}function rC(r,e,a){var i=0,n=0,f=0;re=i=re-16|0,n=r+24|0,eY[0|e](i+8|0,n),f=l[i+12>>2];r:if((l[l[i+8>>2]>>2]&7<<f)>>f!=(0|a))for(eY[0|e](i+8|0,n),n=e=l[i+8>>2],f=l[e>>2],e=l[i+12>>2],l[n>>2]=f&(7<<e^-1)|(7&a)<<e;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}re=i+16|0}function rS(r,e,a){var i,n,f=w(0),b=0;r=r+44|0,b=l[(e<<2)+4236>>2];r:{if((-2&e)==2){r=rX(r,5,b,2140081935);break r}r=rY(r,b,2140081935)}e:{if((0|r)!=2139156720){if((0|r)==2140081935|(0|r)==2141891242)break e;if(i=r,t[2]=i,(f=u())!=f)return w(NaN);if(n=(-1073741825&r)+536870912|0,t[2]=n,f=u(),!(1073741824&r))break e}f=w(w(f*a)*w(.009999999776482582))}return f}function rP(r,e,a){var i,n,f=w(0),b=0;r=r+44|0,b=l[(e<<2)+4220>>2];r:{if((-2&e)==2){r=rX(r,4,b,2140081935);break r}r=rY(r,b,2140081935)}e:{if((0|r)!=2139156720){if((0|r)==2140081935|(0|r)==2141891242)break e;if(i=r,t[2]=i,(f=u())!=f)return w(NaN);if(n=(-1073741825&r)+536870912|0,t[2]=n,f=u(),!(1073741824&r))break e}f=w(w(f*a)*w(.009999999776482582))}return f}function rY(r,e,a){var i=w(0);i=p[(e=(e<<2)+r|0)>>2],e=l[e>>2];r:if(!((0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|i==i)){if(e=l[r+28>>2],i=p[r+28>>2],(0|e)==2139156720|(0|e)==2140081935|(0|e)==2141891242|i==i||(i=p[r+32>>2],!((0|(r=l[r+32>>2]))==2139156720|(0|r)==2140081935|(0|r)==2141891242)&&(e=a,i!=i)))break r;e=r}return e}function rU(r,e){var a,i,n=w(0),f=0;r=r+152|0,f=l[(e<<2)+4236>>2];r:{if((-2&e)==2){r=rX(r,5,f,2140081935);break r}r=rY(r,f,2140081935)}n=w(0);e:if((0|r)!=2139156720){if(n=w(0),(0|r)==2140081935)break e;a:if((0|r)!=2141891242&&(a=r,t[2]=a,(n=u())==n)){i=(-1073741825&r)+536870912|0,t[2]=i,n=u();break e}n=w(NaN)}return eu(n,w(0))}function rM(r,e){var a,i,n=w(0),f=0;r=r+152|0,f=l[(e<<2)+4220>>2];r:{if((-2&e)==2){r=rX(r,4,f,2140081935);break r}r=rY(r,f,2140081935)}n=w(0);e:if((0|r)!=2139156720){if(n=w(0),(0|r)==2140081935)break e;a:if((0|r)!=2141891242&&(a=r,t[2]=a,(n=u())==n)){i=(-1073741825&r)+536870912|0,t[2]=i,n=u();break e}n=w(NaN)}return eu(n,w(0))}function rH(r,e,a,i){var n=0,f=0,t=0;r*=e,t=(n=(n=rl(r))<0?n+1:n)!=n;r:{if(!(!(1e-4>y(n))|t)){r-=n;break r}e:{if(n!=n){r-=n;break e}if(r-=n,!(1e-4>y(n+-1)))break e;r+=1;break r}if(a){r+=1;break r}if(i)break r;f=0;a:if(!t){if(f=1,n>.5)break a;f=1e-4>y(n+-.5)?1:0}r+=f}return r!=r|e!=e?w(NaN):w(r/e)}function rQ(r){var e=w(0),a=0,i=w(0);a=0;r:if((196608&l[r+24>>2])!=131072){e:if(l[r+552>>2]){e=p[r+32>>2];a:if((e==e||(e=p[r+28>>2])>w(0))&&(a=1,e!=w(0)))break r;if((e=p[r+36>>2])==e)break e;if(s[r+4|0]<0){e=w(1);break e}if(e=w(0),!((i=p[r+28>>2])<w(0)))break e;e=w(-i)}a=e!=w(0)}return a}function rz(r,e){var a=w(0);r:{if((0|e)!=2139156720){if((0|e)!=2140081935){if((0|e)!=2141891242)break r;l[r>>2]=2143289344,l[r+4>>2]=3;return}l[r>>2]=0,l[r+4>>2]=1;return}l[r>>2]=0,l[r+4>>2]=2;return}if(t[2]=e,(a=u())!=a){l[r>>2]=2143289344,l[r+4>>2]=0;return}l[r+4>>2]=1073741824&e?2:1,l[r>>2]=(-1073741825&e)+536870912}function rj(r){var e=0,a=0,i=0;a=l[r+24>>2];r:if(8&a){if(e=1,(7168&a)==5120||(e=0,i=l[r+560>>2],a=l[r+556>>2],(0|i)==(0|a)))break r;for(i=(r=i-a>>2)>>>0<=1?1:r,r=0;;){if(e=(196608&(e=l[l[a+(r<<2)>>2]+24>>2]))!=131072&(57344&e)==40960)break r;if((0|i)==(0|(r=r+1|0)))break}}return e}function rD(r){var e,a=0,i=w(0);a=2143289344;r:if(!(w(y(r))==w(1/0)|r!=r||(a=2139156720,r<w(10842021724855044e-35)&r>w(-.00000000000000000010842021724855044)|r==w(0)))){c(r),e=-2147483648&t[2]|1602224127,t[2]=e,i=u(),c(r>w(18446742974197924e3)?i:r<w(-18446742974197924e3)?i:r),a=t[2]-536870912|1073741824}return a}function rx(r){var e,a=0,i=w(0);a=2143289344;r:if(!(w(y(r))==w(1/0)|r!=r||(a=2140081935,r<w(10842021724855044e-35)&r>w(-.00000000000000000010842021724855044)|r==w(0)))){c(r),e=-2147483648&t[2]|1610612735,t[2]=e,i=u(),c(r>w(36893485948395848e3)?i:r<w(-36893485948395848e3)?i:r),a=t[2]-536870912|0}return a}function rJ(r,e){r:{if(e){e=ev(l[e>>2]);break r}e:{if(d[6936]){e=l[1733];break e}A[(e=eo(28))+20>>1]=0,l[e+16>>2]=1065353216,A[e+10>>1]=0,A[e+12>>1]=0,l[e>>2]=0,l[e+24>>2]=0,s[e+9|0]=0,l[e+4>>2]=1,s[e+22|0]=0,l[1733]=e,s[6936]=1,l[1732]=l[1732]+1}e=ev(e)}return l[r+4>>2]=0,l[r+8>>2]=0,l[r>>2]=e,l[e>>2]=r,r}function rK(r,e,a){var i=0,n=0,f=0;r:{if(!e){i=r;break r}for(;r=r9(i=rh(r,e,10),n=ra,246,0)+r|0,s[0|(a=a-1|0)]=48|r,f=e>>>0>9,r=i,e=n,f;);}if(i)for(;a=a-1|0,r=(i>>>0)/10|0,s[0|a]=N(r,246)+i|48,e=i>>>0>9,i=r,e;);return a}function rq(r,e){var a=0,i=0;r:{e:if(a=l[r+556>>2],i=l[r+560>>2],(0|a)!=(0|i)){for(;;){if(l[a>>2]==(0|e))break e;if((0|i)==(0|(a=a+4|0)))break}break r}if((0|a)==(0|i))break r;return e=a+4|0,rm(a,e,i-e|0),l[r+560>>2]=i-4,1}return 0}function r$(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+184>>2]!=(0|a))for(l[((e<<2)+i|0)+184>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r0(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+192>>2]!=(0|a))for(l[((e<<2)+i|0)+192>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r2(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+176>>2]!=(0|a))for(l[((e<<2)+i|0)+176>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r1(r,e){var a=0,i=0,n=0;r:if(l[(a=r+24|0)+16>>2]!=(0|e))for(i=0|eY[9](a),n=e,l[i+16>>2]=n;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r4(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+56>>2]!=(0|a))for(l[((e<<2)+i|0)+56>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r3(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+92>>2]!=(0|a))for(l[((e<<2)+i|0)+92>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r8(r,e,a){var i=0;r:if(l[((i=r+24|0)+(e<<2)|0)+20>>2]!=(0|a))for(l[((e<<2)+i|0)+20>>2]=a;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}function r6(r,e){var a,i=0,n=w(0);r=r+80|0,i=l[(e<<2)+4236>>2];r:{if((-2&e)==2){r=rX(r,5,i,2143289344);break r}r=rY(r,i,2143289344)}return(0|r)==2139156720|(0|r)==2140081935|(0|r)==2141891242?r=1:(a=r,t[2]=a,r=(n=u())==n),r}function r5(r,e){var a,i=0,n=w(0);r=r+80|0,i=l[(e<<2)+4220>>2];r:{if((-2&e)==2){r=rX(r,4,i,2143289344);break r}r=rY(r,i,2143289344)}return(0|r)==2139156720|(0|r)==2140081935|(0|r)==2141891242?r=1:(a=r,t[2]=a,r=(n=u())==n),r}function r9(r,e,a,i){var n=0,f=0,t=0,b=0,o=0,k=0;return k=N(n=a>>>16|0,f=r>>>16|0),n=(65535&(f=((o=N(t=65535&a,b=65535&r))>>>16|0)+N(f,t)|0))+N(n,b)|0,ra=(N(e,a)+k|0)+N(r,i)+(f>>>16)+(n>>>16)|0,65535&o|n<<16}function r7(r,e){var a=0,i=0,n=0;re=i=re-16|0,n=-17&(a=d[r+4|0]),s[r+4|0]=n;r:{if(e){if(l[r+560>>2]!=l[r+556>>2])break r;a=8|n}else a&=231;l[r+8>>2]=e,s[r+4|0]=a,re=i+16|0;return}l[i>>2]=4059,rB(r,i),eX(),k()}function er(r,e,a,i,n){var f=0;if(re=f=re-256|0,!(73728&n|(0|a)<=(0|i))){if(ry(f,255&e,(a=(i=a-i|0)>>>0<256)?i:256),!a)for(;rF(r,f,256),(i=i-256|0)>>>0>255;);rF(r,f,i)}re=f+256|0}function ee(r,e,a){var i=0,n=w(0);r:((-2&a)!=2||(n=p[(i=e- -64|0)>>2],!((0|(i=l[i>>2]))==2139156720|(0|i)==2140081935|(0|i)==2141891242|n==n)))&&(i=l[((l[(a<<2)+4236>>2]<<2)+e|0)+44>>2]);rz(r,i)}function ea(r,e,a){var i=0,n=w(0);r:((-2&a)!=2||(i=l[e+60>>2],n=p[e+60>>2],!((0|i)==2139156720|(0|i)==2140081935|(0|i)==2141891242|n==n)))&&(i=l[((l[(a<<2)+4220>>2]<<2)+e|0)+44>>2]);rz(r,i)}function ei(r){var e=0,a=0,i=0;for(i=l[r>>2];;){if(eO(a=s[0|i])){i=i+1|0,l[r>>2]=i,e=e>>>0<=214748364?(0|(a=a-48|0))>(2147483647^(e=N(e,10)))?-1:a+e|0:-1;continue}break}return e}function en(r,e,a,i){var n=0;l[r+12>>2]=0,l[r+16>>2]=i;r:{if(e){if(e>>>0>=1073741824)break r;n=eo(e<<2)}return l[r>>2]=n,a=(a<<2)+n|0,l[r+8>>2]=a,l[r+12>>2]=(e<<2)+n,l[r+4>>2]=a,r}eP(),k()}function ef(r){var e=0;return(e=l[r+72>>2],l[r+72>>2]=e-1|e,8&(e=l[r>>2]))?(l[r>>2]=32|e,-1):(l[r+4>>2]=0,l[r+8>>2]=0,e=l[r+44>>2],l[r+28>>2]=e,l[r+20>>2]=e,l[r+16>>2]=e+l[r+48>>2],0)}function et(r,e,a,i){r|=0,e|=0,a|=0,i|=0;var n=0;n=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(n=l[n+l[e>>2]>>2]),eY[0|n](e,a,i)}function eb(r,e,a){r|=0,e|=0,a|=0;var i=0;return i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),0|eY[0|i](e,a)}function eo(r){var e=0;r=r||1;r:{for(;;){if(e=rb(r))break r;if(e=l[2022]){eY[0|e]();continue}break}T(),k()}return e}function ek(r,e,a){r|=0,e|=0,a|=0;var i=0;i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),eY[0|i](e,a)}function ec(r,e){r|=0,e|=0;var a=0;return a=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(a=l[a+l[e>>2]>>2]),0|eY[0|a](e)}function eu(r,e){return(c(r),(2147483647&t[2])>>>0<=2139095040)?(c(e),(2147483647&t[2])>>>0>2139095040)?r:w(R(r,e)):e}function es(r,e){return(c(r),(2147483647&t[2])>>>0<=2139095040)?(c(e),(2147483647&t[2])>>>0>2139095040)?r:w(G(r,e)):e}function eA(r,e){r|=0,e|=0;var a=0;return re=a=re-16|0,l[a+8>>2]=e,r=0|eY[0|r](a+8|0),I(l[a+8>>2]),re=a+16|0,0|r}function el(r,e){if(!r)return 0;r:{if(!((-128&e)==57216|e>>>0<=127)){l[1761]=25,r=-1;break r}s[0|r]=e,r=1}return r}function ed(r,e){var a=0,i=0;l[(a=eo(4))>>2]=e,l[(i=eo(4))>>2]=e,X(6955,0|r,7018,4630,192,0|a,7018,4634,193,0|i)}function eh(r,e){var a=0,i=0;l[(a=eo(4))>>2]=e,l[(i=eo(4))>>2]=e,X(6985,0|r,7018,4630,190,0|a,7018,4634,191,0|i)}function ev(r){var e=0,a=0;return re=e=re-16|0,a=rk(eo(588),r),r||(l[e>>2]=2759,rB(0,e),eX(),k()),re=e+16|0,a}function ep(r,e){var a=0;r:{if(1&s[6964]){a=l[1740];break r}a=0|F(1,4400),s[6964]=1,l[1740]=a}Q(0|a,0|r,0|e,0)}function em(r,e){r|=0,e|=0;var a=0;a=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(a=l[a+l[e>>2]>>2]),eY[0|a](e)}function eN(r){return l[(r|=0)>>2]=4508,d[r+4|0]&&ep(l[r+8>>2],1758),I(l[r+8>>2]),0|r}function ew(r){return l[(r|=0)>>2]=4388,d[r+4|0]&&ep(l[r+8>>2],1758),I(l[r+8>>2]),0|r}function ey(r,e,a){return r5(r,e)?rI(r,e,a):((a=rT(r,e,a))==a&&(a=w(-a)),a)}function eE(r){var e=0;(r|=0)&&((e=l[r>>2])&&rE(e),l[1732]=l[1732]-1,rE(r))}function eG(r,e,a){e|=0,a=+a,m[l[(r|=0)>>2]+e>>3]=a}function eR(r,e){return e|=0,+m[l[(r|=0)>>2]+e>>3]}function eg(r){(r|=0)&&eY[l[l[r>>2]+4>>2]](r)}function eZ(r){return r?(l[1761]=r,-1):0}function eW(r,e,a){return w(rP(r,e,a)+rS(r,e,a))}function eV(r,e,a,i){j(0|r,0|e,8,0,0|a,-1,0|i)}function eB(r,e){e|=0,eY[l[(r|=0)>>2]](e)}function eT(r){return 0|eY[0|(r|=0)]()}function eI(r,e){e|=0,eY[0|(r|=0)](e)}function eX(){eY[l[1470]](),eP(),k()}function eO(r){return r-48>>>0<10}function eL(r){(r|=0)&&rE(r)}function e_(r){return 0|(r|=0)}function eF(r){s[(r|=0)+4|0]=1}function eC(r){k()}function eS(){eP(),k()}function eP(){T(),k()}n(e=d,1024,"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"),n(e,4270,"wH8AAAAAAADAfwMAAABpaQB2AHZp"),n(e,4304,"KxsAACkbAABpGwAAYxsAAGkbAABjGwAAaWlpZmlmaQBcGwAALBsAAHZpaQAtGwAAcBsAAGlpaQ=="),n(e,4368,"wwAAAMQAAADF"),n(e,4388,"wwAAAMYAAADHAAAAXBs="),n(e,4416,"KxsAAGkbAABjGwAAaRsAAGMbAABwGwAAaxsAAHAbAABpaWlpAAAAAFwbAABBGwAAXBsAAEMbAABEGwAAcBs="),n(e,4488,"yAAAAMkAAADK"),n(e,4508,"yAAAAMsAAADHAAAARxsAAFwbAABHGw=="),n(e,4544,"XBsAAEcbAABjGwAAXRsAAHZpaWlpAAAAXBsAAEcbAABpGwAAdmlpZgAAAABcGwAARxsAAF0bAAB2aWlpAAAAAF0bAABIGwAAYxsAAF0bAABHGwAAaQBkaWkAdmlpZAAATBsAAEwbAABHGwAAXBsAAEwbAABcGwAATBsAAEsbAABcGwAATBsAAGMbAAAAAAAAXBsAAEwbAABjGwAAahsAAHZpaWlkAAAAXBsAAEwbAABqGwAAYxsAAE0bAABKGwAATRsAAGMbAABKGwAATRsAAGobAABNGwAAahsAAE0bAABjGwAAZGlpaQAAAABpGwAATBsAAGMbAABmaWlpAAAAAFwbAABMGwAATBsAAGQbAABcGwAATBsAAEwbAABkGwAATRsAAEwbAABMGwAATBsAAEwbAABkGwAAXRsAAEwbAABcGwAATBsAAF0bAABcGwAATBsAACkbAABcGwAATBsAAEEbAABdGwAATRsAAAAAAABcGwAATBsAAGobAABqGwAAYxsAAHZpaWRkaQAASRsAAE0b"),n(e,4960,"GQAKABkZGQAAAAAFAAAAAAAACQAAAAALAAAAAAAAAAAZABEKGRkZAwoHAAEACQsYAAAJBgsAAAsABhkAAAAZGRk="),n(e,5041,"DgAAAAAAAAAAGQAKDRkZGQANAAACAAkOAAAACQAOAAAO"),n(e,5099,"DA=="),n(e,5111,"EwAAAAATAAAAAAkMAAAAAAAMAAAM"),n(e,5157,"EA=="),n(e,5169,"DwAAAAQPAAAAAAkQAAAAAAAQAAAQ"),n(e,5215,"Eg=="),n(e,5227,"EQAAAAARAAAAAAkSAAAAAAASAAASAAAaAAAAGhoa"),n(e,5282,"GgAAABoaGgAAAAAAAAk="),n(e,5331,"FA=="),n(e,5343,"FwAAAAAXAAAAAAkUAAAAAAAUAAAU"),n(e,5389,"Fg=="),n(e,5401,"FQAAAAAVAAAAAAkWAAAAAAAWAAAWAAAwMTIzNDU2Nzg5QUJDREVG"),n(e,5476,"0Q=="),n(e,5516,"//////////8="),n(e,5584,"oB8BAAAAAAAF"),n(e,5604,"zA=="),n(e,5628,"zQAAAM4AAACEGw=="),n(e,5652,"Ag=="),n(e,5668,"//////////8="),n(e,5736,"BQ=="),n(e,5748,"zw=="),n(e,5772,"zQAAANAAAACYGwAAAAQ="),n(e,5796,"AQ=="),n(e,5812,"/////wo="),n(e,5880,"0g==");var eY=((a=[null,function(r,e,a,i,n){var f,t,b,o,k,c;return(i|=0,n|=0,(a|=0)?(0|a)!=5:0)?0|rG(f=5736,t=i,b=n,2,3):0|rG(o=5592,k=i,c=n,2,3)},function(r,e,a,i,n,f){r|=0,e=+e,a|=0,i|=0,n|=0,f|=0;var b,k,c=0,u=0,A=0,h=0,v=0,p=0,m=0,w=0,E=0,G=0,R=0,g=0,Z=0,W=0,V=0,B=0,T=0,I=0,X=0;re=m=re-560|0,l[m+44>>2]=0,b=+e,o[0]=b,c=0|t[1],t[0];r:{if((0|c)<0){Z=1,I=1176,k=+(e=-e),o[0]=k,c=0|t[1],t[0];break r}if(2048&n){Z=1,I=1179;break r}I=(Z=1&n)?1182:1177,X=!Z}e:{if((2146435072&c)==2146435072){er(r,32,a,c=Z+3|0,-65537&n),rF(r,I,Z),i=32&f,rF(r,e!=e?i?2450:3390:i?2819:3394,3),er(r,32,a,c,8192^n),w=(0|a)<(0|c)?c:a;break e}V=m+16|0;a:{i:{n:{if(e=function r(e,a){var i,n,f,b=0,k=0,c=0;if(i=+e,o[0]=i,k=0|t[1],c=0|t[0],(0|(b=k>>>20&2047))!=2047){if(!b)return 0==e?b=0:(e=r(18446744073709552e3*e,a),b=l[a>>2]+-64|0),l[a>>2]=b,e;l[a>>2]=b-1022,n=0|c,t[0]=n,f=-2146435073&k|1071644672,t[1]=f,e=+o[0]}return e}(e,m+44|0),0!=(e+=e)){if(c=l[m+44>>2],l[m+44>>2]=c-1,(0|(B=32|f))!=97)break n;break a}if((0|(B=32|f))==97)break a;v=l[m+44>>2],p=(0|i)<0?6:i;break i}v=c-29|0,l[m+44>>2]=v,e*=268435456,p=(0|i)<0?6:i}for(u=R=(m+48|0)+((0|v)>=0?288:0)|0;i=e<4294967296&e>=0?~~e>>>0:0,l[u>>2]=i,u=u+4|0,0!=(e=(e-+(i>>>0))*1e9););f:{if((0|v)<=0){i=v,c=u,A=R;break f}for(A=R,i=v;;){E=(0|i)>=29?29:i,c=u-4|0;t:if(!(A>>>0>c>>>0)){for(i=0;h=l[c>>2],T=i,i=31&E,(63&E)>>>0>=32?(w=h<<i,i=0):(w=(1<<i)-1&h>>>32-i,i=h<<i),T=T+i|0,h=w+G|0,h=r9(i=rh(T,i>>>0>T>>>0?h+1|0:h,1e9),ra,-1e9,0)+T|0,l[c>>2]=h,A>>>0<=(c=c-4|0)>>>0;);if(!i)break t;l[(A=A-4|0)>>2]=i}for(;c=u,A>>>0<c>>>0&&!l[(u=c-4|0)>>2];);if(i=l[m+44>>2]-E|0,l[m+44>>2]=i,u=c,!((0|i)>0))break}}if((0|i)<0)for(W=((p+25>>>0)/9|0)+1|0,G=(0|B)==102;;){w=(0|(i=0-i|0))>=9?9:i;b:{if(c>>>0<=A>>>0){u=l[A>>2];break b}for(E=1e9>>>w|0,h=-1<<w^-1,i=0,u=A;T=i,i=l[u>>2],l[u>>2]=T+(i>>>w|0),i=N(E,i&h),(u=u+4|0)>>>0<c>>>0;);if(u=l[A>>2],!i)break b;l[c>>2]=i,c=c+4|0}if(i=w+l[m+44>>2]|0,l[m+44>>2]=i,A=(!u<<2)+A|0,u=G?R:A,c=c-u>>2>(0|W)?u+(W<<2)|0:c,!((0|i)<0))break}i=0;o:if(!(c>>>0<=A>>>0)){if(i=N(R-A>>2,9),u=10,(h=l[A>>2])>>>0<10)break o;for(;i=i+1|0,u=N(u,10),h>>>0>=u>>>0;);}if((0|(u=(p-((0|B)!=102?i:0)|0)-((0|B)==103&(0|p)!=0)|0))<(N(c-R>>2,9)-9|0)){if(h=(0|(E=u+9216|0))/9|0,v=((((0|v)<0?4:292)+m|0)+(h<<2)|0)-4048|0,u=10,(0|(w=E+N(h,-9)|0))<=7)for(;u=N(u,10),(0|(w=w+1|0))!=8;);W=((E=l[v>>2])>>>0)/(u>>>0)|0,G=N(W,u),h=v+4|0;k:if(!((0|E)==(0|G)&(0|h)==(0|c))){E=E-G|0;c:(1&W||(e=9007199254740992,!(!(1&s[v-4|0])|((0|u)!=1e9|A>>>0>=v>>>0))))&&(e=9007199254740994);if(g=(0|c)==(0|h)?1:1.5,g=(h=u>>>1|0)>>>0>E>>>0?.5:(0|h)==(0|E)?g:1.5,45!=d[0|I]|X||(g=-g,e=-e),l[v>>2]=G,e+g==e)break k;if(i=u+G|0,l[v>>2]=i,i>>>0>=1e9)for(;l[v>>2]=0,(v=v-4|0)>>>0<A>>>0&&(l[(A=A-4|0)>>2]=0),i=l[v>>2]+1|0,l[v>>2]=i,i>>>0>999999999;);if(i=N(R-A>>2,9),u=10,(h=l[A>>2])>>>0<10)break k;for(;i=i+1|0,u=N(u,10),h>>>0>=u>>>0;);}c=c>>>0>(u=v+4|0)>>>0?u:c}for(;h=c,!(E=c>>>0<=A>>>0)&&!l[(c=h-4|0)>>2];);u:{if((0|B)!=103){v=8&n;break u}if(p=((c=(0|(u=p||1))>(0|i)&(0|i)>-5)?-1^i:-1)+u|0,f=(c?-1:-2)+f|0,v=8&n)break u;c=-9;s:if(!E&&(v=l[h-4>>2])){if(w=10,c=0,(v>>>0)%10|0)break s;for(;u=c,c=c+1|0,w=N(w,10),!((v>>>0)%(w>>>0)|0););c=-1^u}if(u=N(h-R>>2,9),(-33&f)==70){v=0,p=(0|(c=(0|(c=(c+u|0)-9|0))>0?c:0))>(0|p)?p:c;break u}v=0,p=(0|(c=(0|(c=((i+u|0)+c|0)-9|0))>0?c:0))>(0|p)?p:c}if(w=-1,(((E=v|p)?2147483645:2147483646)|0)<(0|p))break e;G=(((0|E)!=0)+p|0)+1|0,u=-33&f;A:{if((0|u)==70){if((2147483647^G)<(0|i))break e;c=(0|i)>0?i:0;break A}if(c=rK(((c=i>>31)^i)-c|0,0,V),(V-c|0)<=1)for(;s[0|(c=c-1|0)]=48,(V-c|0)<2;);if(s[0|(W=c-2|0)]=f,s[c-1|0]=(0|i)<0?45:43,(0|(c=V-W|0))>(2147483647^G))break e}if((0|(i=c+G|0))>(2147483647^Z))break e;er(r,32,a,G=i+Z|0,n),rF(r,I,Z),er(r,48,a,G,65536^n);l:{d:{h:{if((0|u)==70){for(i=8|(f=m+16|0),v=9|f,A=u=A>>>0>R>>>0?R:A;;){c=rK(l[A>>2],0,v);v:{if((0|u)!=(0|A)){if(m+16>>>0>=c>>>0)break v;for(;s[0|(c=c-1|0)]=48,m+16>>>0<c>>>0;);break v}if((0|c)!=(0|v))break v;s[m+24|0]=48,c=i}if(rF(r,c,v-c|0),!(R>>>0>=(A=A+4|0)>>>0))break}if(E&&rF(r,4206,1),(0|p)<=0|A>>>0>=h>>>0)break h;for(;;){if((c=rK(l[A>>2],0,v))>>>0>m+16>>>0)for(;s[0|(c=c-1|0)]=48,m+16>>>0<c>>>0;);if(rF(r,c,(0|p)>=9?9:p),c=p-9|0,h>>>0<=(A=A+4|0)>>>0)break d;if(i=(0|p)>9,p=c,!i)break}break d}p:if(!((0|p)<0))for(R=A>>>0<h>>>0?h:A+4|0,i=8|(f=m+16|0),h=9|f,u=A;;){c=rK(l[u>>2],0,h),(0|h)==(0|c)&&(s[m+24|0]=48,c=i);m:{if((0|u)!=(0|A)){if(m+16>>>0>=c>>>0)break m;for(;s[0|(c=c-1|0)]=48,m+16>>>0<c>>>0;);break m}if(rF(r,c,1),c=c+1|0,!(v|p))break m;rF(r,4206,1)}if(f=h-c|0,rF(r,c,(0|f)>(0|p)?p:f),p=p-f|0,R>>>0<=(u=u+4|0)>>>0)break p;if(!((0|p)>=0))break}er(r,48,p+18|0,18,0),rF(r,W,V-W|0);break l}c=p}er(r,48,c+9|0,9,0)}er(r,32,a,G,8192^n),w=(0|a)<(0|G)?G:a;break e}v=(f<<26>>31&9)+I|0;N:if(!(i>>>0>11)){for(c=12-i|0,g=16;g*=16,c=c-1|0;);if(45==d[0|v]){e=-(g+(-e-g));break N}e=e+g-g}for(R=2|Z,A=32&f,c=(u=l[m+44>>2])>>31,c=rK((c^u)-c|0,0,V),(0|V)==(0|c)&&(s[m+15|0]=48,c=m+15|0),s[0|(p=c-2|0)]=f+15,s[c-1|0]=(0|u)<0?45:43,c=8&n,u=m+16|0;f=u,h=2147483648>y(e)?~~e:-2147483648,s[0|u]=A|d[h+5424|0],!(c|(0|i)>0)&0==(e=(e-+(0|h))*16)|((u=f+1|0)-(m+16|0)|0)!=1||(s[f+1|0]=46,u=f+2|0),0!=e;);if(w=-1,(2147483645-(f=(c=V-p|0)+R|0)|0)<(0|i))break e;w:{y:if(i&&!(((A=u-(m+16|0)|0)-2|0)>=(0|i))){i=i+2|0;break w}i=A=u-(m+16|0)|0}er(r,32,a,f=i+f|0,n),rF(r,v,R),er(r,48,a,f,65536^n),rF(r,m+16|0,A),er(r,48,i-A|0,0,0),rF(r,p,c),er(r,32,a,f,8192^n),w=(0|a)<(0|f)?f:a}return re=m+560|0,0|w},function(r,e){r|=0,e|=0;var a,i,n=0,f=0,b=0,k=0,c=0,u=0,s=0,A=0,d=0,h=0,v=0,p=0,N=0,w=0,y=0,E=0;n=e,e=l[e>>2]+7&-8,l[n>>2]=e+16,N=r,s=l[e>>2],f=l[e+4>>2],v=r=l[e+12>>2],re=c=re-32|0,r&=2147483647,u=r,b=r-1006698496|0,r=r-1140785152|0,e=n=l[e+8>>2];r:{if((0|b)==(0|r)&e>>>0<e>>>0|r>>>0>b>>>0){if(n=v<<4|(r=n)>>>28,e=r<<4|f>>>28,r=n,(0|(f&=268435455))==134217728&(0|s)!=0|f>>>0>134217728){r=r+1073741824|0,r=(e=e+1|0)?r:r+1|0;break r}if(r=r+1073741824|0,s|(0|f)!=134217728)break r;r=(e=(f=1&e)+e|0)>>>0<f>>>0?r+1|0:r;break r}if(!(!e&(0|u)==2147418112?!(f|s):u>>>0<2147418112)){n=v<<4|(r=n)>>>28,e=r<<4|f>>>28,r=524287&n|2146959360;break r}if(e=0,r=2146435072,u>>>0>1140785151||(r=0,(p=u>>>16|0)>>>0<15249))break r;e=s,r=f,u=b=65535&v|65536,h=n,k=n,A=p-15233|0;e:{if(64&A){n=e,e=31&(b=A+-64|0),(63&b)>>>0>=32?(r=n<<e,k=0):(r=(1<<e)-1&n>>>32-e|r<<e,k=n<<e),b=r,e=0,r=0;break e}if(!A)break e;d=k,k=31&A,(63&A)>>>0>=32?(n=d<<k,k=0):(n=(1<<k)-1&d>>>32-k|b<<k,k=d<<k),b=n,w=k,d=e,k=31&(n=64-A|0),(63&n)>>>0>=32?(n=0,e=r>>>k|0):(n=r>>>k|0,e=((1<<k)-1&r)<<32-k|d>>>k),k=w|e,b|=n,e=31&A,(63&A)>>>0>=32?(n=d<<e,e=0):(n=(1<<e)-1&d>>>32-e|r<<e,e=d<<e),r=n}l[c+16>>2]=e,l[c+20>>2]=r,l[c+24>>2]=k,l[c+28>>2]=b,e=15361-p|0;a:{if(64&e){f=h,r=31&(e=e+-64|0),(63&e)>>>0>=32?(n=0,s=u>>>r|0):(n=u>>>r|0,s=((1<<r)-1&u)<<32-r|f>>>r),f=n,h=0,u=0;break a}if(!e)break a;b=h,n=31&(r=64-e|0),(63&r)>>>0>=32?(r=b<<n,k=0):(r=(1<<n)-1&b>>>32-n|u<<n,k=b<<n),b=s,s=31&e,(63&e)>>>0>=32?(n=0,b=f>>>s|0):(n=f>>>s|0,b=((1<<s)-1&f)<<32-s|b>>>s),s=k|b,f=r|n,b=h,n=31&e,(63&e)>>>0>=32?(r=0,h=u>>>n|0):(r=u>>>n|0,h=((1<<n)-1&u)<<32-n|b>>>n),u=r}if(l[c>>2]=s,l[c+4>>2]=f,l[c+8>>2]=h,l[c+12>>2]=u,e=l[c+8>>2],r=l[c+12>>2]<<4|e>>>28,e<<=4,n=l[c>>2],e=(u=l[c+4>>2])>>>28|e,(0|(f=268435455&u))==134217728&(0|(n|=(l[c+16>>2]|l[c+24>>2]|(l[c+20>>2]|l[c+28>>2]))!=0))!=0|f>>>0>134217728){r=(e=e+1|0)?r:r+1|0;break r}if(n|(0|f)!=134217728)break r;r=(n=e)>>>0>(e=e+(1&e)|0)>>>0?r+1|0:r}re=c+32|0,a=0|e,t[0]=a,i=-2147483648&v|r,t[1]=i,y=N,E=+o[0],m[y>>3]=E},e_,function(r,e){e|=0,l[(r|=0)+4>>2]=7,l[r>>2]=e},function(r,e){e|=0,l[(r|=0)+4>>2]=10,l[r>>2]=e},function(r,e){e|=0,l[(r|=0)+4>>2]=13,l[r>>2]=e},e_,e_,e_,e_,e_,e_,function(r,e,a,i,n,f){r|=0,e|=0,a=w(a),i|=0,n=w(n),f|=0;var t=0;re=t=re-16|0,e=l[l[e>>2]+4>>2],eY[l[l[e>>2]+8>>2]](t,e,a,i,n,f),p[r>>2]=m[t>>3],p[r+4>>2]=m[t+8>>3],re=t+16|0},function(r){r|=0,r=l[l[r>>2]+8>>2],eY[l[l[r>>2]+8>>2]](r)},rn,function(r){return 6952},eg,function(r,e,a,i,n,f){r|=0,e|=0,a=w(a),i|=0,n=w(n),f|=0;var t=0,b=0;return re=t=re-16|0,b=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(b=l[b+l[e>>2]>>2]),eY[0|b](t,e,a,i,n,f),r=eo(16),e=l[t+12>>2],l[r+8>>2]=l[t+8>>2],l[r+12>>2]=e,e=l[t+4>>2],l[r>>2]=l[t>>2],l[r+4>>2]=e,re=t+16|0,0|r},function(r){return 6956},e_,e_,eg,eF,eB,eA,function(r){r|=0;var e=0;return s[(e=eo(12))+4|0]=0,l[e+8>>2]=l[r>>2],l[r>>2]=0,l[e>>2]=4368,0|e},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0,f=0,t=0;if(re=i=re-32|0,(n=l[e>>2])>>>0<2147483632){r:{e:{if(n>>>0>=11){t=eo(f=(15|n)+1|0),l[i+16>>2]=-2147483648|f,l[i+8>>2]=t,l[i+12>>2]=n,f=n+t|0;break e}if(s[i+19|0]=n,f=(t=i+8|0)+n|0,!n)break r}ru(t,e+4|0,n)}return s[0|f]=0,l[i>>2]=a,eY[0|r](i+24|0,i+8|0,i),U(l[i+24>>2]),r=l[i+24>>2],I(0|r),I(l[i>>2]),s[i+19|0]<0&&rE(l[i+8>>2]),re=i+32|0,0|r}T(),k()},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0;i=r,n=0|Y((s[e+11|0]<0?l[e>>2]:e)|0,6956,l[a>>2]),l[i>>2]=n},function(r){return 6976},eg,em,function(r){return 6979},e_,e_,eg,eF,eB,eA,function(r){r|=0;var e=0;return s[(e=eo(12))+4|0]=0,l[e+8>>2]=l[r>>2],l[r>>2]=0,l[e>>2]=4488,0|e},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0;i=r,n=0|Y((s[e+11|0]<0?l[e>>2]:e)|0,6979,l[a>>2]),l[i>>2]=n},function(r){return 6982},eE,eT,function(){var r=0,e=0;return e=eo(4),A[(r=eo(28))+20>>1]=0,l[r+16>>2]=1065353216,A[r+10>>1]=0,A[r+12>>1]=0,l[r>>2]=0,l[r+24>>2]=0,s[r+9|0]=0,l[r+4>>2]=1,s[r+22|0]=0,l[1732]=l[1732]+1,l[e>>2]=r,0|e},eI,eE,function(r,e,a){e|=0,a|=0,s[(l[(r|=0)>>2]+e|0)+20|0]=a},et,function(r,e){r|=0,e=w(e);var a=0,i=0,n=0;if(re=a=re-16|0,r=l[r>>2],!(e>=w(0))){l[a>>2]=2262,re=i=re-16|0,l[i+12>>2]=a;r:if(!r){if(d[6936]){r=l[1733];break r}A[(r=eo(28))+20>>1]=0,l[r+16>>2]=1065353216,A[r+10>>1]=0,A[r+12>>1]=0,l[r>>2]=0,l[r+24>>2]=0,s[r+9|0]=0,l[r+4>>2]=1,s[r+22|0]=0,l[1733]=r,s[6936]=1,l[1732]=l[1732]+1}n=l[r+4>>2];e:{if(d[r+9|0]){eY[0|n](r,0,5,0,4215,a);break e}eY[0|n](r,0,5,4215,a)}re=i+16|0,eX(),k()}p[r+16>>2]=e==w(0)?w(0):e,re=a+16|0},function(r,e,a){r|=0,e|=0,a=w(a);var i=0;i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),eY[0|i](e,a)},function(r,e){e|=0,s[l[(r|=0)>>2]+11|0]=e},ek,function(r,e){e|=0,s[l[(r|=0)>>2]+10|0]=e},function(r,e){return e|=0,d[(l[(r|=0)>>2]+e|0)+20|0]},eb,function(r){return d[l[(r|=0)>>2]+11|0]},ec,function(r){return d[l[(r|=0)>>2]+10|0]},function(){return 0|ry(eo(48),0,48)},eL,function(){var r=0;return l[(r=eo(16))>>2]=0,l[r+4>>2]=0,l[r+8>>2]=0,l[r+12>>2]=0,0|r},eL,function(){var r=0;return l[(r=eo(16))+8>>2]=0,l[r+12>>2]=0,l[r>>2]=0,0|r},eL,eR,eG,function(r,e){return e|=0,l[l[(r|=0)>>2]+e>>2]},function(r,e,a){e|=0,a|=0,l[l[(r|=0)>>2]+e>>2]=a},function(r){return 6987},rL,eT,function(){return 0|rJ(eo(12),0)},function(r,e){return e|=0,0|eY[0|(r|=0)](e)},function(r){return r|=0,0|rJ(eo(12),r)},eI,rL,function(r){r|=0;var e=0,a=0,i=0,n=0;re=i=re-624|0,e=l[r+4>>2],l[r+4>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e),e=l[r+8>>2],l[r+8>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e);r:{if(r=l[r>>2],l[r+560>>2]==l[r+556>>2]){if(l[r+552>>2])break r;r_(r+556|0),n=s[r+4|0],e=rk(i+32|0,l[r+568>>2]),a=l[e+4>>2],l[r>>2]=l[e>>2],l[r+4>>2]=a,a=l[e+20>>2],l[r+16>>2]=l[e+16>>2],l[r+20>>2]=a,a=l[e+12>>2],l[r+8>>2]=l[e+8>>2],l[r+12>>2]=a,ru(r+24|0,e+24|0,204),ru(r+228|0,e+228|0,328),(a=l[r+556>>2])&&(l[r+560>>2]=a,rE(a)),l[r+556>>2]=l[e+556>>2],l[r+560>>2]=l[e+560>>2],l[r+564>>2]=l[e+564>>2],l[r+584>>2]=l[e+584>>2],a=l[e+580>>2],l[r+576>>2]=l[e+576>>2],l[r+580>>2]=a,a=l[e+572>>2],l[r+568>>2]=l[e+568>>2],l[r+572>>2]=a,(0|n)<0&&(s[r+4|0]=128|d[r+4|0],l[r+24>>2]=-909&l[r+24>>2]|520),re=i+624|0;return}l[i+16>>2]=3252,rB(r,i+16|0),eX(),k()}l[i>>2]=2046,rB(r,i),eX(),k()},em,function(r,e){r|=0,e|=0;var a,i,n=0,f=w(0),b=0,o=0,k=w(0),c=0,A=0,h=0,v=w(0);r:{A=l[e>>2],e=l[r>>2];e:if(!((l[A+24>>2]^l[e+24>>2])&8388607)){v=p[A+40>>2],b=l[A+40>>2],f=p[e+40>>2];a:{i:{if((0|(n=l[e+40>>2]))!=2139156720){if(k=w(NaN),r=3,(0|n)==2141891242)break a;if((0|n)!=2140081935)break i;k=w(0),r=1;break a}r=2;break a}if(r=0,f!=f)break a;a=(-1073741825&n)+536870912|0,t[2]=a,k=u(),r=1073741824&n?2:1}n:{f:{if((0|b)!=2139156720){if(f=w(NaN),n=3,(0|b)==2141891242)break n;if((0|b)!=2140081935)break f;f=w(0),n=1;break n}f=w(0),n=2;break n}if(n=0,v!=v)break n;i=(-1073741825&b)+536870912|0,t[2]=i,f=u(),n=1073741824&b?2:1}if((0|n)!=(0|r)|!(!r|k!=k&f!=f|w(y(w(k-f)))<w(9999999747378752e-20))||(n=e+44|0,o=A+44|0,l[n>>2]!=l[o>>2]))break e;r=0;t:{for(;;){if(b=r,(0|(r=r+1|0))==9)break t;if(l[(c=r<<2)+n>>2]!=l[o+c>>2])break}if(b>>>0<8)break e}if(n=e+80|0,o=A+80|0,l[n>>2]!=l[o>>2])break e;r=0;b:{for(;;){if(b=r,(0|(r=r+1|0))==9)break b;if(l[(c=r<<2)+n>>2]!=l[o+c>>2])break}if(b>>>0<8)break e}if(n=e+116|0,o=A+116|0,l[n>>2]!=l[o>>2])break e;r=0;o:{for(;;){if(b=r,(0|(r=r+1|0))==9)break o;if(l[(c=r<<2)+n>>2]!=l[o+c>>2])break}if(b>>>0<8)break e}if(n=e+152|0,o=A+152|0,l[n>>2]!=l[o>>2])break e;r=0;k:{for(;;){if(b=r,(0|(r=r+1|0))==9)break k;if(l[(c=r<<2)+n>>2]!=l[o+c>>2])break}if(b>>>0<8)break e}if(n=e+188|0,o=A+188|0,l[n>>2]!=l[o>>2])break e;r=0;c:{for(;;){if(b=r,(0|(r=r+1|0))==3)break c;if(l[(c=r<<2)+n>>2]!=l[o+c>>2])break}if(b>>>0<2)break e}for(c=A+200|0,h=e+200|0,r=0,b=1;;){u:if(r<<=2,n=l[r+c>>2],o=l[r+h>>2],b&&(b=0,r=1,(0|n)==(0|o)))continue;break}if((0|n)!=(0|o))break e;for(c=A+208|0,h=e+208|0,r=0,b=1;;){s:if(r<<=2,n=l[r+c>>2],o=l[r+h>>2],b&&(b=0,r=1,(0|n)==(0|o)))continue;break}if((0|n)!=(0|o))break e;for(c=A+216|0,h=e+216|0,r=0,b=1;;){A:if(r<<=2,n=l[r+c>>2],o=l[r+h>>2],b&&(b=0,r=1,(0|n)==(0|o)))continue;break}if((0|n)!=(0|o)||(r=(k=p[A+28>>2])!=k,f=p[e+28>>2],(0|r)==(f==f|0)|!(r|f!=f)&f!=k)||(f=p[e+32>>2],k=p[A+32>>2],(f==f|0)==(k!=k|0)|f==f&f!=k)||(r=(k=p[A+36>>2])!=k,f=p[e+36>>2],(0|r)==(f==f|0)|!r&f!=k))break e;if(r=0,(f=p[e+224>>2])!=f&&(r=1,(k=p[A+224>>2])!=k)||(k=f,f=p[A+224>>2],k==f|f!=f&r))break r}for(ru(e+24|0,A+24|0,204);;){if(4&(r=d[e+4|0]))break r;if(s[e+4|0]=4|r,(r=l[e+20>>2])&&eY[0|r](e),l[e+308>>2]=2143289344,!(e=l[e+552>>2]))break}}},ek,function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>16&3)!=(0|e))for(l[r+24>>2]=-196609&a|e<<16&196608;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},ek,function(r,e,a){e|=0,a=+a,r4(l[(r|=0)>>2],e,rx(w(a)))},function(r,e,a,i){r|=0,e|=0,a|=0,i=+i;var n=0;n=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(n=l[n+l[e>>2]>>2]),eY[0|n](e,a,i)},function(r,e,a){e|=0,a=+a,r4(l[(r|=0)>>2],e,rD(w(a)))},function(r,e){e|=0,rC(l[(r|=0)>>2],5,e)},function(r,e){e|=0,rC(l[(r|=0)>>2],6,e)},function(r,e){e|=0,rC(l[(r|=0)>>2],7,e)},function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>2&3)!=(0|e))for(l[r+24>>2]=-13&a|e<<2&12;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>18&3)!=(0|e))for(l[r+24>>2]=-786433&a|e<<18&786432;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>4&7)!=(0|e))for(l[r+24>>2]=-113&a|e<<4&112;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e,a){e|=0,a=+a,r8(l[(r|=0)>>2],e,rx(w(a)))},function(r,e,a){e|=0,a=+a,r8(l[(r|=0)>>2],e,rD(w(a)))},function(r,e){e|=0,r8(l[(r|=0)>>2],e,2141891242)},function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>20&3)!=(0|e))for(l[r+24>>2]=-3145729&a|e<<20&3145728;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){r|=0,e|=0;var a=0;r:if(r=l[r>>2],((a=l[r+24>>2])>>>22&1)!=(0|e))for(l[r+24>>2]=-4194305&a|e<<22&4194304;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){r|=0,e=+e;var a=0,i=w(0),n=w(0);r:if(i=w(e),n=p[(r=l[r>>2])+28>>2],!(i==n|i!=i&n!=n))for(p[r+28>>2]=i;;){if(4&(a=d[r+4|0]))break r;if(s[r+4|0]=4|a,(a=l[r+20>>2])&&eY[0|a](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e,a){r|=0,e|=0,a=+a;var i=0;i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),eY[0|i](e,a)},function(r,e){e=+e,r1(l[(r|=0)>>2],rx(w(e)))},function(r,e){e=+e,r1(l[(r|=0)>>2],rD(w(e)))},function(r){r1(l[(r|=0)>>2],2141891242)},function(r,e){r|=0,e=+e;var a=0,i=w(0),n=w(0);r:if(i=w(e),n=p[(r=l[r>>2])+32>>2],!(i==n|i!=i&n!=n))for(p[r+32>>2]=i;;){if(4&(a=d[r+4|0]))break r;if(s[r+4|0]=4|a,(a=l[r+20>>2])&&eY[0|a](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){r|=0,e=+e;var a=0,i=w(0),n=w(0);r:if(i=w(e),n=p[(r=l[r>>2])+36>>2],!(i==n|i!=i&n!=n))for(p[r+36>>2]=i;;){if(4&(a=d[r+4|0]))break r;if(s[r+4|0]=4|a,(a=l[r+20>>2])&&eY[0|a](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e){e=+e,r2(l[(r|=0)>>2],0,rx(w(e)))},function(r,e){e=+e,r2(l[(r|=0)>>2],0,rD(w(e)))},function(r){r2(l[(r|=0)>>2],0,2141891242)},function(r,e){e=+e,r2(l[(r|=0)>>2],1,rx(w(e)))},function(r,e){e=+e,r2(l[(r|=0)>>2],1,rD(w(e)))},function(r){r2(l[(r|=0)>>2],1,2141891242)},function(r,e){e=+e,r$(l[(r|=0)>>2],0,rx(w(e)))},function(r,e){e=+e,r$(l[(r|=0)>>2],0,rD(w(e)))},function(r,e){e=+e,r$(l[(r|=0)>>2],1,rx(w(e)))},function(r,e){e=+e,r$(l[(r|=0)>>2],1,rD(w(e)))},function(r,e){e=+e,r0(l[(r|=0)>>2],0,rx(w(e)))},function(r,e){e=+e,r0(l[(r|=0)>>2],0,rD(w(e)))},function(r,e){e=+e,r0(l[(r|=0)>>2],1,rx(w(e)))},function(r,e){e=+e,r0(l[(r|=0)>>2],1,rD(w(e)))},function(r,e){r|=0,e=+e;var a=0,i=w(0),n=w(0);r:if(i=w(e),n=p[(r=l[r>>2])+224>>2],!(i==n|i!=i&n!=n))for(p[r+224>>2]=i;;){if(4&(a=d[r+4|0]))break r;if(s[r+4|0]=4|a,(a=l[r+20>>2])&&eY[0|a](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e,a){r|=0,e|=0,a=+a;var i=0,n=0;r:if(e=(r=l[r>>2])+(e<<2)|0,(0|(n=l[e+152>>2]))!=(0|(i=rx(w(a)))))for(l[e+152>>2]=i;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r,e,a){e|=0,a=+a,r3(l[(r|=0)>>2],e,rx(w(a)))},function(r,e,a){e|=0,a=+a,r3(l[(r|=0)>>2],e,rD(w(a)))},function(r,e,a){r|=0,e|=0,a=+a;var i=0,n=0;r:if(e=(r=l[r>>2])+(e<<2)|0,(0|(n=l[e+188>>2]))!=(0|(i=rx(w(a)))))for(l[e+188>>2]=i;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},function(r){return 3&h[l[(r|=0)>>2]+26>>1]},ec,function(r,e,a){r|=0,e|=0,a|=0;var i,n=0,f=0,b=w(0);n=2,b=p[(e=l[e>>2]+(a<<2)|0)+80>>2];r:{e:{if((0|(e=l[e+80>>2]))!=2139156720){if(f=NaN,(0|e)==2141891242){n=3;break r}if((0|e)!=2140081935)break e;f=0,n=1}break r}if(b!=b){n=0;break r}n=1073741824&e?2:1,f=+(i=(-1073741825&e)+536870912|0,t[2]=i,u())}m[r+8>>3]=f,l[r>>2]=n},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0;return re=i=re-16|0,n=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(n=l[n+l[e>>2]>>2]),eY[0|n](i,e,a),r=eo(16),e=l[i+12>>2],l[r+8>>2]=l[i+8>>2],l[r+12>>2]=e,e=l[i+4>>2],l[r>>2]=l[i>>2],l[r+4>>2]=e,re=i+16|0,0|r},function(r){return l[l[(r|=0)>>2]+24>>2]>>>7&7},function(r){return l[l[(r|=0)>>2]+24>>2]>>>10&7},function(r){return l[l[(r|=0)>>2]+24>>2]>>>13&7},function(r){return l[l[(r|=0)>>2]+24>>2]>>>2&3},function(r){return l[l[(r|=0)>>2]+24>>2]>>>18&3},function(r){return l[l[(r|=0)>>2]+24>>2]>>>4&7},function(r,e,a){r|=0,e|=0,a|=0;var i,n=0,f=0,b=w(0);n=2,b=p[(e=l[e>>2]+(a<<2)|0)+44>>2];r:{e:{if((0|(e=l[e+44>>2]))!=2139156720){if(f=NaN,(0|e)==2141891242){n=3;break r}if((0|e)!=2140081935)break e;f=0,n=1}break r}if(b!=b){n=0;break r}n=1073741824&e?2:1,f=+(i=(-1073741825&e)+536870912|0,t[2]=i,u())}m[r+8>>3]=f,l[r>>2]=n},function(r,e){r|=0,e|=0;var a,i=0,n=w(0),f=w(0);n=w(NaN),f=p[(e=l[e>>2])+40>>2],e=l[e+40>>2],i=3;r:if((0|e)!=2141891242){if((0|e)==2140081935){n=w(0),i=1;break r}if((0|e)==2139156720){n=w(0),i=2;break r}if(i=0,f!=f)break r;a=(-1073741825&e)+536870912|0,t[2]=a,n=u(),i=1073741824&e?2:1}l[r>>2]=i,m[r+8>>3]=n},function(r,e){r|=0,e|=0;var a=0,i=0;return re=a=re-16|0,i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),eY[0|i](a,e),r=eo(16),e=l[a+12>>2],l[r+8>>2]=l[a+8>>2],l[r+12>>2]=e,e=l[a+4>>2],l[r>>2]=l[a>>2],l[r+4>>2]=e,re=a+16|0,0|r},function(r){r|=0;var e=w(0);return+((e=p[l[r>>2]+32>>2])==e?e:w(0))},function(r,e){r|=0,e|=0;var a=0;return a=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(a=l[a+l[e>>2]>>2]),+eY[0|a](e)},function(r){r|=0;var e=w(0);return(e=p[(r=l[r>>2])+36>>2])!=e&&(e=d[l[r+568>>2]+10|0]?w(1):w(0)),+e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+200>>2];r:{e:{if((0|(e=l[e+200>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+204>>2];r:{e:{if((0|(e=l[e+204>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+208>>2];r:{e:{if((0|(e=l[e+208>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+212>>2];r:{e:{if((0|(e=l[e+212>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+216>>2];r:{e:{if((0|(e=l[e+216>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r,e){r|=0,e|=0;var a,i=0,n=0,f=w(0);f=p[(e=l[e>>2])+220>>2];r:{e:{if((0|(e=l[e+220>>2]))!=2139156720){if(n=NaN,i=3,(0|e)==2141891242)break r;if((0|e)!=2140081935)break e;n=0,i=1;break r}i=2;break r}if(i=0,f!=f)break r;n=+(a=(-1073741825&e)+536870912|0,t[2]=a,u()),i=1073741824&e?2:1}e=i,m[r+8>>3]=n,l[r>>2]=e},function(r){r|=0;var e=w(0);return+((e=p[l[r>>2]+224>>2])!=e?w(NaN):e)},function(r,e){r|=0,e|=0;var a,i=0,n=w(0);n=p[(r=l[r>>2]+(e<<2)|0)+152>>2];r:{e:if(!((0|(r=l[r+152>>2]))==2139156720|(0|r)==2140081935)){if(i=NaN,(0|r)==2141891242|n!=n)break r;if((0|r)==2139156720)break e;return+(a=(-1073741825&r)+536870912|0,t[2]=a,u())}i=0}return+i},function(r,e,a){r|=0,e|=0,a|=0;var i=0;return i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),+eY[0|i](e,a)},function(r){return l[l[(r|=0)>>2]+24>>2]>>>20&3},function(r){return l[l[(r|=0)>>2]+24>>2]>>>22&1},function(r,e,a){r|=0,e|=0,a|=0;var i,n=0,f=0,b=w(0);n=2,b=p[(e=l[e>>2]+(a<<2)|0)+116>>2];r:{e:{if((0|(e=l[e+116>>2]))!=2139156720){if(f=NaN,(0|e)==2141891242){n=3;break r}if((0|e)!=2140081935)break e;f=0,n=1}break r}if(b!=b){n=0;break r}n=1073741824&e?2:1,f=+(i=(-1073741825&e)+536870912|0,t[2]=i,u())}m[r+8>>3]=f,l[r>>2]=n},function(r,e){r|=0,e|=0;var a,i=w(0),n=w(0);n=p[(r=l[r>>2]+(e<<2)|0)+188>>2];r:{e:if(!((0|(r=l[r+188>>2]))==2139156720|(0|r)==2140081935)){if(i=w(NaN),(0|r)==2141891242|n!=n)break r;if((0|r)==2139156720)break e;return w((a=(-1073741825&r)+536870912|0,t[2]=a,u()))}i=w(0)}return w(i)},function(r,e,a){r|=0,e|=0,a|=0;var i=0;return i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),w(w(eY[0|i](e,a)))},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0,f=0,t=0,b=0,o=0,c=0,u=0,A=0,h=0;re=b=re-80|0,r=l[r>>2];r:{e:{if(o=l[e>>2],!l[o+552>>2]){if(l[r+8>>2])break e;t=(e=l[r+556>>2])+(a<<2)|0,i=l[r+560>>2],f=l[(n=r+564|0)>>2];a:{if(i>>>0<f>>>0){if((0|i)==(0|t)){l[t>>2]=o,l[r+560>>2]=t+4;break a}if(a=i,e=i-4|0,i>>>0>e>>>0)for(;l[a>>2]=l[e>>2],a=a+4|0,i>>>0>(e=e+4|0)>>>0;);l[r+560>>2]=a,(0|(e=t+4|0))!=(0|i)&&(e=i-e|0,rm(i-(-4&e)|0,t,e)),l[t>>2]=o;break a}if((i=(i-e>>2)+1|0)>>>0>=1073741824)break r;f=(e=f-e|0)>>>1|0,a=l[(n=en(b+32|0,e>>>0>=2147483644?1073741823:i>>>0<f>>>0?f:i,a,n))+8>>2];i:if((0|a)==l[n+12>>2]){if(e=l[n+4>>2],i=l[n>>2],e>>>0>i>>>0){a=rm(c=(i=-(((e-i>>2)+1|0)/2)<<2)+e|0,f=e,e=a-e|0)+e|0,l[n+8>>2]=a,l[n+4>>2]=i+l[n+4>>2];break i}f=en(b+56|0,e=(0|a)==(0|i)?1:a-i>>1,e>>>2|0,l[n+16>>2]),i=l[f+8>>2],e=l[n+4>>2],a=l[n+8>>2];n:{if((0|e)==(0|a)){a=i,i=e;break n}for(a=(a-e|0)+i|0;l[i>>2]=l[e>>2],e=e+4|0,(0|(i=i+4|0))!=(0|a););e=l[n+8>>2],i=l[n+4>>2]}if(c=l[n>>2],l[n>>2]=l[f>>2],l[f>>2]=c,l[n+4>>2]=l[f+4>>2],l[f+4>>2]=i,l[n+8>>2]=a,l[f+8>>2]=e,u=l[n+12>>2],l[n+12>>2]=l[f+12>>2],l[f+12>>2]=u,(0|e)!=(0|i)&&(l[f+8>>2]=((i-e|0)+3&-4)+e),!c)break i;rE(c),a=l[n+8>>2]}if(l[a>>2]=o,l[n+8>>2]=l[n+8>>2]+4,e=l[r+556>>2],a=t-e|0,A=n,h=rm(l[n+4>>2]-a|0,e,a),l[A+4>>2]=h,i=l[r+560>>2]-t|0,t=rm(l[n+8>>2],t,i),e=l[r+556>>2],l[r+556>>2]=l[n+4>>2],l[n+4>>2]=e,a=l[r+560>>2],l[r+560>>2]=i+t,l[n+8>>2]=a,i=l[r+564>>2],l[r+564>>2]=l[n+12>>2],l[n>>2]=e,l[n+12>>2]=i,(0|e)!=(0|a)&&(l[n+8>>2]=a+((e-a|0)+3&-4)),!e)break a;rE(e)}for(l[o+552>>2]=r;!(4&(e=d[r+4|0]))&&(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,r=l[r+552>>2]););re=b+80|0;return}l[b+16>>2]=4006,rB(r,b+16|0),eX(),k()}l[b>>2]=4139,rB(r,b),eX(),k()}T(),k()},et,function(r,e){r|=0,e|=0;var a=0,i=0,n=0,f=0;re=n=re-320|0,r=l[r>>2];r:if(l[r+560>>2]!=l[r+556>>2]){if(i=l[e>>2],e=l[i+552>>2],!rq(r,i))break r;if((0|r)==(0|e)){for(l[(e=a=ry(n,0,288))+16>>2]=2143289344,l[e+20>>2]=2143289344,ry(e+24|0,0,49),l[e+80>>2]=2143289344,f=e+288|0,e=e+96|0;l[e+16>>2]=-1082130432,l[e+20>>2]=-1082130432,l[e+8>>2]=0,l[e+12>>2]=0,l[e>>2]=-1082130432,l[e+4>>2]=-1082130432,(0|f)!=(0|(e=e+24|0)););l[a+312>>2]=-1082130432,l[a+316>>2]=-1082130432,l[a+304>>2]=0,l[a+308>>2]=0,l[a+296>>2]=-1082130432,l[a+300>>2]=-1082130432,l[a+288>>2]=2143289344,l[a+292>>2]=2143289344,ru(i+228|0,a,320),l[i+552>>2]=0}for(;;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}}re=n+320|0},ek,function(r){return r|=0,r=l[r>>2],l[r+560>>2]-l[r+556>>2]>>2},ec,function(r){return(r|=0,r=l[l[r>>2]+552>>2])?l[r>>2]:0},ec,function(r,e){r|=0,e|=0;var a=0,i=0;a=l[r>>2],r=l[a+556>>2];r:!(l[a+560>>2]-r>>2>>>0<=e>>>0)&&(r=l[r+(e<<2)>>2])&&(i=l[r>>2]);return 0|i},eb,function(r){return(2&d[l[(r|=0)>>2]+4|0])>>>1|0},ec,function(r,e){r|=0,e|=0;var a=0;r:if(((2&(a=d[(r=l[r>>2])+4|0]))>>>1|0)!=(0|e))for(s[r+4|0]=253&a|(e?2:0);;){if(4&(e=d[r+4|0]))break r;if(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,!(r=l[r+552>>2]))break}},ek,function(r,e){r|=0,e|=0;var a=0;a=l[r+4>>2],l[r+4>>2]=e,a&&eY[l[l[a>>2]+4>>2]](a),r7(l[r>>2],14)},ek,function(r){r|=0;var e=0;e=l[r+4>>2],l[r+4>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e),r7(l[r>>2],0)},function(r,e){r|=0,e|=0;var a=0;a=l[r+8>>2],l[r+8>>2]=e,a&&eY[l[l[a>>2]+4>>2]](a),l[l[r>>2]+20>>2]=15},ek,function(r){r|=0;var e=0;e=l[r+8>>2],l[r+8>>2]=0,e&&eY[l[l[e>>2]+4>>2]](e),l[l[r>>2]+20>>2]=0},function(r){r|=0;var e=0,a=0;if(re=a=re-16|0,r=l[r>>2],l[r+8>>2]){for(;!(4&(e=d[r+4|0]))&&(s[r+4|0]=4|e,(e=l[r+20>>2])&&eY[0|e](r),l[r+308>>2]=2143289344,r=l[r+552>>2]););re=a+16|0;return}l[a>>2]=1024,rB(r,a),eX(),k()},function(r){return(4&d[l[(r|=0)>>2]+4|0])>>>2|0},ec,function(r,e,a,i){r|=0,e=+e,a=+a,i|=0;var n,f,b,o,k=w(0),c=w(0),s=0,A=w(0),h=0,v=w(0),m=0,N=w(0),E=0;re=E=re+-64|0,s=l[r>>2],ry(E+8|0,0,56),l[1735]=l[1735]+1,rc(s),A=w(e),c=p[s+572>>2];r:{e:{a:switch(0|(r=l[s+576>>2])){case 0:case 3:break e;default:break a}k=p[s+572>>2];i:{if(!((0|r)!=1|k!=k)){if(!((k=c)<w(0)))break i;break e}n:{if(!((0|r)!=2|k!=k)){if(A!=A|c<w(0))break e;break n}k=w(NaN);f:switch(r-1|0){case 1:break n;case 0:break f;default:break i}k=c;break i}k=w(w(c*A)*w(.009999999776482582))}N=w(k+w(rP(s,2,A)+rS(s,2,A))),m=1;break r}k=w(0),c=p[s+216>>2];t:{b:{r=l[s+216>>2];o:{k:if((0|r)!=2139156720){if((0|r)==2140081935)break t;m=4276;c:{if((0|r)!=2141891242){if(c==c)break c;m=4268}switch(k=p[m>>2],l[m+4>>2]-1|0){case 0:break o;case 1:break k;default:break b}}if(n=(-1073741825&r)+536870912|0,t[2]=n,k=u(),!(1073741824&r))break o}k=w(w(k*A)*w(.009999999776482582))}if(k!=k)break b;k=w(0);u:if((0|r)!=2139156720){if((0|r)==2140081935)break t;h=4276;s:{if((0|r)!=2141891242){if(c==c)break s;h=4268}switch(k=p[h>>2],N=w(NaN),m=2,l[h+4>>2]-1|0){case 1:break u;case 0:break t;default:break r}}if(f=(-1073741825&r)+536870912|0,t[2]=f,k=u(),!(1073741824&r))break t}N=w(w(k*A)*w(.009999999776482582)),m=2;break r}m=A==A,N=A;break r}m=2,N=k}v=w(a),c=p[s+580>>2];A:{l:{d:switch(0|(r=l[s+584>>2])){case 0:case 3:break l;default:break d}k=p[s+580>>2];h:{if(!((0|r)!=1|k!=k)){if(!((k=c)<w(0)))break h;break l}v:{if(!((0|r)!=2|k!=k)){if(v!=v|c<w(0))break l;break v}k=w(NaN);p:switch(r-1|0){case 1:break v;case 0:break p;default:break h}k=c;break h}k=w(w(c*v)*w(.009999999776482582))}c=w(k+w(rP(s,0,A)+rS(s,0,A))),r=1;break A}k=w(0),c=p[s+220>>2];m:{N:{r=l[s+220>>2];w:{y:if((0|r)!=2139156720){if((0|r)==2140081935)break m;h=4276;E:{if((0|r)!=2141891242){if(c==c)break E;h=4268}switch(k=p[h>>2],l[h+4>>2]-1|0){case 0:break w;case 1:break y;default:break N}}if(b=(-1073741825&r)+536870912|0,t[2]=b,k=u(),!(1073741824&r))break w}k=w(w(k*v)*w(.009999999776482582))}if(k!=k)break N;k=w(0);G:if((0|r)!=2139156720){if((0|r)==2140081935)break m;h=4276;R:{if((0|r)!=2141891242){if(c==c)break R;h=4268}switch(k=p[h>>2],c=w(NaN),r=2,l[h+4>>2]-1|0){case 0:break m;case 1:break G;default:break A}}if(o=(-1073741825&r)+536870912|0,t[2]=o,k=u(),!(1073741824&r))break m}c=w(w(k*v)*w(.009999999776482582)),r=2;break A}r=v==v,c=v;break A}r=2,c=k}rt(s,N,c,i,m,r,A,v,1,0,l[s+568>>2],E+8|0,0,l[1735])&&(rO(s,3&d[s+300|0],A,v,A),function r(e,a,i,n){var f=0,t=0,b=0,o=0,k=0,c=0,u=0,s=0,A=0,h=0,v=0,m=w(0),N=w(0),E=w(0),G=0,R=w(0);r:if(0!=a){if(m=p[e+248>>2],N=p[e+244>>2],E=p[e+232>>2],s=+p[e+228>>2],f=(b=8&d[e+4|0])>>>3|0,G=e,R=rH(s,a,0,f),p[G+228>>2]=R,A=+E,G=e,R=rH(A,a,0,f),p[G+232>>2]=R,f=(k=rl((h=+N)*a))!=k,t=(c=rl((v=+m)*a))!=c,i=s+i,o=f|(o=1e-4>y(k))?!o|f:!(1e-4>y(k+-1)),f=(0|b)!=0,G=e,R=w(rH(i+h,a,o&f,!o&f)-rH(i,a,0,f)),p[G+244>>2]=R,n=A+n,t=t|(b=1e-4>y(c))?!b|t:!(1e-4>y(c+-1)),G=e,R=w(rH(n+v,a,f&t,!t&f)-rH(n,a,0,f)),p[G+248>>2]=R,f=l[e+560>>2],t=l[e+556>>2],(0|f)==(0|t))break r;for(t=(f=f-t>>2)>>>0<=1?1:f;f=l[e+556>>2],f=l[e+560>>2]-f>>2>>>0>u>>>0?l[f+(u<<2)>>2]:0,r(f,a,i,n),(0|t)!=(0|(u=u+1|0)););}}(s,+p[l[s+568>>2]+16>>2],0,0)),re=E- -64|0},function(r,e,a,i,n){r|=0,e|=0,a=+a,i=+i,n|=0;var f=0;f=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(f=l[f+l[e>>2]>>2]),eY[0|f](e,a,i,n)},function(r){return+p[l[(r|=0)>>2]+228>>2]},function(r){return+p[l[(r|=0)>>2]+236>>2]},function(r){return+p[l[(r|=0)>>2]+232>>2]},function(r){return+p[l[(r|=0)>>2]+240>>2]},function(r){return+p[l[(r|=0)>>2]+244>>2]},function(r){return+p[l[(r|=0)>>2]+248>>2]},function(r,e){r|=0,e|=0,e=l[e>>2],m[r>>3]=p[e+228>>2],m[r+8>>3]=p[e+236>>2],m[r+16>>3]=p[e+232>>2],m[r+24>>3]=p[e+240>>2],m[r+32>>3]=p[e+244>>2],m[r+40>>3]=p[e+248>>2]},function(r,e){r|=0,e|=0;var a=0,i=0;return re=a=re-48|0,i=l[r>>2],e=((r=l[r+4>>2])>>1)+e|0,1&r&&(i=l[i+l[e>>2]>>2]),eY[0|i](a,e),r=ru(eo(48),a,48),re=a+48|0,0|r},function(r,e){r|=0,e|=0;var a=0;if(re=a=re-16|0,r=l[r>>2],(0|e)<6){re=a+16|0;r:{e:switch(e-4|0){case 0:if(e=r+260|0,(3&d[r+300|0])==2)break r;e=r+252|0;break r;case 1:if(e=r+252|0,(3&d[r+300|0])==2)break r;e=r+260|0;break r;default:break e}e=(r+(e<<2)|0)+252|0}return+p[e>>2]}l[a>>2]=1871,rB(r,a),eX(),k()},function(r,e){r|=0,e|=0;var a=0;if(re=a=re-16|0,r=l[r>>2],(0|e)<6){re=a+16|0;r:{e:switch(e-4|0){case 0:if(e=r+276|0,(3&d[r+300|0])==2)break r;e=r+268|0;break r;case 1:if(e=r+268|0,(3&d[r+300|0])==2)break r;e=r+276|0;break r;default:break e}e=(r+(e<<2)|0)+268|0}return+p[e>>2]}l[a>>2]=1871,rB(r,a),eX(),k()},function(r,e){r|=0,e|=0;var a=0;if(re=a=re-16|0,r=l[r>>2],(0|e)<6){re=a+16|0;r:{e:switch(e-4|0){case 0:if(e=r+292|0,(3&d[r+300|0])==2)break r;e=r+284|0;break r;case 1:if(e=r+284|0,(3&d[r+300|0])==2)break r;e=r+292|0;break r;default:break e}e=(r+(e<<2)|0)+284|0}return+p[e>>2]}l[a>>2]=1871,rB(r,a),eX(),k()},eR,eG,eR,eG,rw,ew,function(r){rE(ew(r|=0))},function(r,e,a,i,n,f){r|=0,e|=0,a=w(a),i|=0,n=w(n),f|=0;var t=0,b=0,o=0;re=t=re-48|0,o=l[e+8>>2];r:{if(1&s[6972]){e=l[1742];break r}e=0|F(5,4416),s[6972]=1,l[1742]=e}l[t+40>>2]=f,p[t+32>>2]=n,l[t+24>>2]=i,p[t+16>>2]=a,b=+H(0|e,0|o,2873,t+12|0,t+16|0);e:{if(b<4294967296&b>=0){e=~~b>>>0;break e}e=0}i=l[t+12>>2],f=l[e+4>>2],l[r>>2]=l[e>>2],l[r+4>>2]=f,f=l[e+12>>2],l[r+8>>2]=l[e+8>>2],l[r+12>>2]=f,M(0|i),re=t+48|0},eC,eS,eN,function(r){rE(eN(r|=0))},function(r){ep(l[(r|=0)+8>>2],3244)},eC,function(r){return 0|K(l[(r|=0)+60>>2])},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0,f=0,t=0,b=0,o=0,k=0;re=i=re-32|0,n=l[r+28>>2],l[i+16>>2]=n,f=l[r+20>>2],l[i+28>>2]=a,l[i+24>>2]=e,e=f-n|0,l[i+20>>2]=e,f=e+a|0,o=2,e=i+16|0;r:{for(;;){e:{a:{i:{if(!eZ(0|J(l[r+60>>2],0|e,0|o,i+12|0))){if((0|(t=l[i+12>>2]))==(0|f))break i;if((0|t)>=0)break a;break e}if((0|f)!=-1)break e}e=l[r+44>>2],l[r+28>>2]=e,l[r+20>>2]=e,l[r+16>>2]=e+l[r+48>>2],r=a;break r}n=((k=(b=l[e+4>>2])>>>0<t>>>0)<<3)+e|0,b=t-(k?b:0)|0,l[n>>2]=b+l[n>>2],l[(e=(k?12:4)+e|0)>>2]=l[e>>2]-b,f=f-t|0,o=o-k|0,e=n;continue}break}if(l[r+28>>2]=0,l[r+16>>2]=0,l[r+20>>2]=0,l[r>>2]=32|l[r>>2],r=0,(0|o)==2)break r;r=a-l[e+4>>2]|0}return re=i+32|0,0|r},function(r,e,a,i){r|=0,e|=0,a|=0,i|=0;var n=0;return re=n=re-16|0,r=eZ(0|z(l[r+60>>2],0|e,0|a,255&i,n+8|0)),re=n+16|0,ra=r?-1:l[n+12>>2],(r?-1:l[n+8>>2])|0},function(r){return 0},function(r,e,a,i){return ra=0,0},function(r,e,a){r|=0,e|=0,a|=0;var i=0,n=0,f=0,t=0,b=0;return n=l[r+84>>2],f=l[n>>2],i=l[n+4>>2],b=l[r+28>>2],t=l[r+20>>2]-b|0,(t=i>>>0<t>>>0?i:t)&&(ru(f,b,t),f=t+l[n>>2]|0,l[n>>2]=f,i=l[n+4>>2]-t|0,l[n+4>>2]=i),(i=a>>>0>i>>>0?i:a)&&(ru(f,e,i),f=i+l[n>>2]|0,l[n>>2]=f,l[n+4>>2]=l[n+4>>2]-i),s[0|f]=0,e=l[r+44>>2],l[r+28>>2]=e,l[r+20>>2]=e,0|a},eS]).set=function(r,e){this[r]=e},a.get=function(r){return this[r]},a);function eU(){return b.byteLength/65536|0}return{E:function(){for(var r=0,e=0,a=0;a=(e=r<<4)+5888|0,l[e+5892>>2]=a,l[e+5896>>2]=a,(0|(r=r+1|0))!=64;);rd(48),l[1736]=16,l[1737]=0,rn(),l[1737]=l[1748],l[1748]=6944,l[1749]=194,l[1750]=0,rw(),l[1750]=l[1748],l[1748]=6996},F:function(r){r|=0;var e=0,a=0,i=0,n=0,f=0;re=a=re-96|0,l[a>>2]=r,re=i=re-16|0,l[i+12>>2]=a,re=r=re-144|0,r=ru(r,5440,144),e=f=a+16|0,l[r+44>>2]=e,l[r+20>>2]=e,n=(n=-2-e|0)>>>0>=2147483647?2147483647:n,l[r+48>>2]=n,e=e+n|0,l[r+28>>2]=e,l[r+16>>2]=e,rG(r,2201,a,0,0),n&&(s[(e=l[r+20>>2])-((0|e)==l[r+16>>2])|0]=0),re=r+144|0,re=i+16|0,r=f;r:{if(3&r)for(;;){if(!d[0|r])break r;if(!(3&(r=r+1|0)))break}for(;e=r,r=r+4|0,!((-1^(i=l[e>>2]))&i-16843009&-2139062144););for(;e=(r=e)+1|0,d[0|r];);}return r=(e=rb(r=(r-f|0)+1|0))?ru(e,f,r):0,re=a+96|0,0|r},G:function(){var r=0;if(r=l[1748])for(;eY[l[r>>2]](),r=l[r+4>>2];);},H:rb,I:eY,J:rE,K:function(r,e,a,i,n){return e|=0,a|=0,i|=0,n|=0,0|eY[0|(r|=0)](e,a,i,n)}}}(r)}(r)}Object.assign(u,n),n=null,u.wasmBinary&&(l=u.wasmBinary),u.noExitRuntime;var c=Error;l=[];var u,s,A,l,d,h=!1;function v(r,e,a){a=e+a;for(var i="";!(e>=a);){var n=r[e++];if(!n)break;if(128&n){var f=63&r[e++];if(192==(224&n))i+=String.fromCharCode((31&n)<<6|f);else{var t=63&r[e++];65536>(n=224==(240&n)?(15&n)<<12|f<<6|t:(7&n)<<18|f<<12|t<<6|63&r[e++])?i+=String.fromCharCode(n):(n-=65536,i+=String.fromCharCode(55296|n>>10,56320|1023&n))}}else i+=String.fromCharCode(n)}return i}function p(){var r=d.buffer;u.HEAP8=N=new Int8Array(r),u.HEAP16=y=new Int16Array(r),u.HEAP32=G=new Int32Array(r),u.HEAPU8=w=new Uint8Array(r),u.HEAPU16=E=new Uint16Array(r),u.HEAPU32=R=new Uint32Array(r),u.HEAPF32=g=new Float32Array(r),u.HEAPF64=Z=new Float64Array(r)}var m=u.INITIAL_MEMORY||16777216;65536<=m||O("INITIAL_MEMORY should be larger than STACK_SIZE, was "+m+"! (STACK_SIZE=65536)"),d=u.wasmMemory?u.wasmMemory:new function(){this.buffer=new ArrayBuffer(m/65536*65536)},p(),m=d.buffer.byteLength;var N,w,y,E,G,R,g,Z,W,V=[],B=[],T=[],I=0,X=null;function O(r){throw u.onAbort&&u.onAbort(r),b(r="Aborted("+r+")"),h=!0,A(r=new c(r+". Build with -sASSERTIONS for more info.")),r}function L(r){return r.startsWith("data:application/octet-stream;base64,")}if(!L(a="yoga-asm.wasm")){var _=a;a=u.locateFile?u.locateFile(_,f):f+_}function F(r){for(;0<r.length;)r.shift()(u)}function C(r){if(void 0===r)return"_unknown";var e=(r=r.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=e&&57>=e?"_"+r:r}function S(r,e){return r=C(r),function(){return e.apply(this,arguments)}}var P=[{},{value:void 0},{value:null},{value:!0},{value:!1}],Y=[];function U(r){var e=Error,a=S(r,function(e){this.name=r,this.message=e,void 0!==(e=Error(e).stack)&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))});return a.prototype=Object.create(e.prototype),a.prototype.constructor=a,a.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},a}var M=void 0;function H(r){throw new M(r)}var Q=r=>(r||H("Cannot use deleted val. handle = "+r),P[r].value),z=r=>{switch(r){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var e=Y.length?Y.pop():P.length;return P[e]={fa:1,value:r},e}},j=void 0,D=void 0;function x(r){for(var e="";w[r];)e+=D[w[r++]];return e}var J=[];function K(){for(;J.length;){var r=J.pop();r.L.Z=!1,r.delete()}}var q=void 0,$={};function rr(r,e){for(void 0===e&&H("ptr should not be undefined");r.P;)e=r.aa(e),r=r.P;return e}var re={};function ra(r){var e=x(r=rq(r));return r0(r),e}function ri(r,e){var a=re[r];return void 0===a&&H(e+" has unknown type "+ra(r)),a}function rn(){}var rf=!1;function rt(r){--r.count.value,0===r.count.value&&(r.S?r.T.V(r.S):r.O.M.V(r.N))}var rb={},ro=void 0;function rk(r){throw new ro(r)}function rc(r,e){return e.O&&e.N||rk("makeClassHandle requires ptr and ptrType"),!!e.T!=!!e.S&&rk("Both smartPtrType and smartPtr must be specified"),e.count={value:1},ru(Object.create(r,{L:{value:e}}))}function ru(r){return"undefined"==typeof FinalizationRegistry?(ru=r=>r,r):(rf=new FinalizationRegistry(r=>{rt(r.L)}),ru=r=>{var e=r.L;return e.S&&rf.register(r,{L:e},r),r},rn=r=>{rf.unregister(r)},ru(r))}var rs={};function rA(r){for(;r.length;){var e=r.pop();r.pop()(e)}}function rl(r){return this.fromWireType(G[r>>2])}var rd={},rh={};function rv(r,e,a){function i(e){(e=a(e)).length!==r.length&&rk("Mismatched type converter count");for(var i=0;i<r.length;++i)rm(r[i],e[i])}r.forEach(function(r){rh[r]=e});var n=Array(e.length),f=[],t=0;e.forEach((r,e)=>{re.hasOwnProperty(r)?n[e]=re[r]:(f.push(r),rd.hasOwnProperty(r)||(rd[r]=[]),rd[r].push(()=>{n[e]=re[r],++t===f.length&&i(n)}))}),0===f.length&&i(n)}function rp(r){switch(r){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw TypeError("Unknown type size: "+r)}}function rm(r,e,a={}){if(!("argPackAdvance"in e))throw TypeError("registerType registeredInstance requires argPackAdvance");var i=e.name;if(r||H('type "'+i+'" must have a positive integer typeid pointer'),re.hasOwnProperty(r)){if(a.ta)return;H("Cannot register type '"+i+"' twice")}re[r]=e,delete rh[r],rd.hasOwnProperty(r)&&(e=rd[r],delete rd[r],e.forEach(r=>r()))}function rN(r){H(r.L.O.M.name+" instance already deleted")}function rw(){}function ry(r,e,a){if(void 0===r[e].R){var i=r[e];r[e]=function(){return r[e].R.hasOwnProperty(arguments.length)||H("Function '"+a+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+r[e].R+")!"),r[e].R[arguments.length].apply(this,arguments)},r[e].R=[],r[e].R[i.Y]=i}}function rE(r,e,a,i,n,f,t,b){this.name=r,this.constructor=e,this.W=a,this.V=i,this.P=n,this.oa=f,this.aa=t,this.ma=b,this.ia=[]}function rG(r,e,a){for(;e!==a;)e.aa||H("Expected null or instance of "+a.name+", got an instance of "+e.name),r=e.aa(r),e=e.P;return r}function rR(r,e){return null===e?(this.da&&H("null is not a valid "+this.name),0):(e.L||H('Cannot pass "'+rL(e)+'" as a '+this.name),e.L.N||H("Cannot pass deleted object as a pointer of type "+this.name),rG(e.L.N,e.L.O.M,this.M))}function rg(r,e){if(null===e){if(this.da&&H("null is not a valid "+this.name),this.ca){var a=this.ea();return null!==r&&r.push(this.V,a),a}return 0}if(e.L||H('Cannot pass "'+rL(e)+'" as a '+this.name),e.L.N||H("Cannot pass deleted object as a pointer of type "+this.name),!this.ba&&e.L.O.ba&&H("Cannot convert argument of type "+(e.L.T?e.L.T.name:e.L.O.name)+" to parameter type "+this.name),a=rG(e.L.N,e.L.O.M,this.M),this.ca)switch(void 0===e.L.S&&H("Passing raw pointer to smart pointer is illegal"),this.Aa){case 0:e.L.T===this?a=e.L.S:H("Cannot convert argument of type "+(e.L.T?e.L.T.name:e.L.O.name)+" to parameter type "+this.name);break;case 1:a=e.L.S;break;case 2:if(e.L.T===this)a=e.L.S;else{var i=e.clone();a=this.wa(a,z(function(){i.delete()})),null!==r&&r.push(this.V,a)}break;default:H("Unsupporting sharing policy")}return a}function rZ(r,e){return null===e?(this.da&&H("null is not a valid "+this.name),0):(e.L||H('Cannot pass "'+rL(e)+'" as a '+this.name),e.L.N||H("Cannot pass deleted object as a pointer of type "+this.name),e.L.O.ba&&H("Cannot convert argument of type "+e.L.O.name+" to parameter type "+this.name),rG(e.L.N,e.L.O.M,this.M))}function rW(r,e,a,i){this.name=r,this.M=e,this.da=a,this.ba=i,this.ca=!1,this.V=this.wa=this.ea=this.ja=this.Aa=this.va=void 0,void 0!==e.P?this.toWireType=rg:(this.toWireType=i?rR:rZ,this.U=null)}function rV(r,e){var a,i,n=(r=x(r)).includes("j")?(a=r,i=[],function(){if(i.length=0,Object.assign(i,arguments),a.includes("j")){var r=u["dynCall_"+a];r=i&&i.length?r.apply(null,[e].concat(i)):r.call(null,e)}else r=W.get(e).apply(null,i);return r}):W.get(e);return"function"!=typeof n&&H("unknown function pointer with signature "+r+": "+e),n}var rB=void 0;function rT(r,e){var a=[],i={};throw e.forEach(function r(e){i[e]||re[e]||(rh[e]?rh[e].forEach(r):(a.push(e),i[e]=!0))}),new rB(r+": "+a.map(ra).join([", "]))}function rI(r,e,a,i,n){var f=e.length;2>f&&H("argTypes array size mismatch! Must at least get return value and 'this' types!");var t=null!==e[1]&&null!==a,b=!1;for(a=1;a<e.length;++a)if(null!==e[a]&&void 0===e[a].U){b=!0;break}var o="void"!==e[0].name,k=f-2,c=Array(k),u=[],s=[];return function(){if(arguments.length!==k&&H("function "+r+" called with "+arguments.length+" arguments, expected "+k+" args!"),s.length=0,u.length=t?2:1,u[0]=n,t){var a=e[1].toWireType(s,this);u[1]=a}for(var f=0;f<k;++f)c[f]=e[f+2].toWireType(s,arguments[f]),u.push(c[f]);if(f=i.apply(null,u),b)rA(s);else for(var A=t?1:2;A<e.length;A++){var l=1===A?a:c[A-2];null!==e[A].U&&e[A].U(l)}return o?e[0].fromWireType(f):void 0}}function rX(r,e){for(var a=[],i=0;i<r;i++)a.push(R[e+4*i>>2]);return a}function rO(r){4<r&&0==--P[r].fa&&(P[r]=void 0,Y.push(r))}function rL(r){if(null===r)return"null";var e=typeof r;return"object"===e||"array"===e||"function"===e?r.toString():""+r}function r_(r,e){for(var a="",i=0;!(i>=e/2);++i){var n=y[r+2*i>>1];if(0==n)break;a+=String.fromCharCode(n)}return a}function rF(r,e,a){if(void 0===a&&(a=2147483647),2>a)return 0;a-=2;var i=e;a=a<2*r.length?a/2:r.length;for(var n=0;n<a;++n)y[e>>1]=r.charCodeAt(n),e+=2;return y[e>>1]=0,e-i}function rC(r){return 2*r.length}function rS(r,e){for(var a=0,i="";!(a>=e/4);){var n=G[r+4*a>>2];if(0==n)break;++a,65536<=n?(n-=65536,i+=String.fromCharCode(55296|n>>10,56320|1023&n)):i+=String.fromCharCode(n)}return i}function rP(r,e,a){if(void 0===a&&(a=2147483647),4>a)return 0;var i=e;a=i+a-4;for(var n=0;n<r.length;++n){var f=r.charCodeAt(n);if(55296<=f&&57343>=f&&(f=65536+((1023&f)<<10)|1023&r.charCodeAt(++n)),G[e>>2]=f,(e+=4)+4>a)break}return G[e>>2]=0,e-i}function rY(r){for(var e=0,a=0;a<r.length;++a){var i=r.charCodeAt(a);55296<=i&&57343>=i&&++a,e+=4}return e}var rU={};function rM(r){var e=rU[r];return void 0===e?x(r):e}var rH=[],rQ=[],rz=[null,[],[]];M=u.BindingError=U("BindingError"),u.count_emval_handles=function(){for(var r=0,e=5;e<P.length;++e)void 0!==P[e]&&++r;return r},u.get_first_emval=function(){for(var r=5;r<P.length;++r)if(void 0!==P[r])return P[r];return null},j=u.PureVirtualError=U("PureVirtualError");for(var rj=Array(256),rD=0;256>rD;++rD)rj[rD]=String.fromCharCode(rD);D=rj,u.getInheritedInstanceCount=function(){return Object.keys($).length},u.getLiveInheritedInstances=function(){var r,e=[];for(r in $)$.hasOwnProperty(r)&&e.push($[r]);return e},u.flushPendingDeletes=K,u.setDelayFunction=function(r){q=r,J.length&&q&&q(K)},ro=u.InternalError=U("InternalError"),rw.prototype.isAliasOf=function(r){if(!(this instanceof rw&&r instanceof rw))return!1;var e=this.L.O.M,a=this.L.N,i=r.L.O.M;for(r=r.L.N;e.P;)a=e.aa(a),e=e.P;for(;i.P;)r=i.aa(r),i=i.P;return e===i&&a===r},rw.prototype.clone=function(){if(this.L.N||rN(this),this.L.$)return this.L.count.value+=1,this;var r=ru,e=Object,a=e.create,i=Object.getPrototypeOf(this),n=this.L;return r=r(a.call(e,i,{L:{value:{count:n.count,Z:n.Z,$:n.$,N:n.N,O:n.O,S:n.S,T:n.T}}})),r.L.count.value+=1,r.L.Z=!1,r},rw.prototype.delete=function(){this.L.N||rN(this),this.L.Z&&!this.L.$&&H("Object already scheduled for deletion"),rn(this),rt(this.L),this.L.$||(this.L.S=void 0,this.L.N=void 0)},rw.prototype.isDeleted=function(){return!this.L.N},rw.prototype.deleteLater=function(){return this.L.N||rN(this),this.L.Z&&!this.L.$&&H("Object already scheduled for deletion"),J.push(this),1===J.length&&q&&q(K),this.L.Z=!0,this},rW.prototype.pa=function(r){return this.ja&&(r=this.ja(r)),r},rW.prototype.ga=function(r){this.V&&this.V(r)},rW.prototype.argPackAdvance=8,rW.prototype.readValueFromPointer=rl,rW.prototype.deleteObject=function(r){null!==r&&r.delete()},rW.prototype.fromWireType=function(r){function e(){return this.ca?rc(this.M.W,{O:this.va,N:i,T:this,S:r}):rc(this.M.W,{O:this,N:r})}var a,i=this.pa(r);if(!i)return this.ga(r),null;var n=$[rr(this.M,i)];if(void 0!==n)return 0===n.L.count.value?(n.L.N=i,n.L.S=r,n.clone()):(n=n.clone(),this.ga(r),n);if(!(n=rb[n=this.M.oa(i)]))return e.call(this);n=this.ba?n.ka:n.pointerType;var f=function r(e,a,i){return a===i?e:void 0===i.P?null:null===(e=r(e,a,i.P))?null:i.ma(e)}(i,this.M,n.M);return null===f?e.call(this):this.ca?rc(n.M.W,{O:n,N:f,T:this,S:r}):rc(n.M.W,{O:n,N:f})},rB=u.UnboundTypeError=U("UnboundTypeError");var rx="function"==typeof atob?atob:function(r){var e="",a=0;r=r.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r.charAt(a++)),n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r.charAt(a++)),f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r.charAt(a++)),t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r.charAt(a++));i=i<<2|n>>4,n=(15&n)<<4|f>>2;var b=(3&f)<<6|t;e+=String.fromCharCode(i),64!==f&&(e+=String.fromCharCode(n)),64!==t&&(e+=String.fromCharCode(b))}while(a<r.length);return e},rJ={q:function(r,e,a){r=x(r),e=ri(e,"wrapper"),a=Q(a);var i=[].slice,n=e.M,f=n.W,t=n.P.W,b=n.P.constructor;for(var o in r=S(r,function(){n.P.ia.forEach((function(r){if(this[r]===t[r])throw new j("Pure virtual function "+r+" must be implemented in JavaScript")}).bind(this)),Object.defineProperty(this,"__parent",{value:f}),this.__construct.apply(this,i.call(arguments))}),f.__construct=function(){this===f&&H("Pass correct 'this' to __construct");var r=b.implement.apply(void 0,[this].concat(i.call(arguments)));rn(r);var e=r.L;r.notifyOnDestruction(),e.$=!0,Object.defineProperties(this,{L:{value:e}}),ru(this),r=rr(n,r=e.N),$.hasOwnProperty(r)?H("Tried to register registered instance: "+r):$[r]=this},f.__destruct=function(){this===f&&H("Pass correct 'this' to __destruct"),rn(this);var r=this.L.N;r=rr(n,r),$.hasOwnProperty(r)?delete $[r]:H("Tried to unregister unregistered instance: "+r)},r.prototype=Object.create(f),a)r.prototype[o]=a[o];return z(r)},k:function(r){var e=rs[r];delete rs[r];var a=e.ea,i=e.V,n=e.ha;rv([r],n.map(r=>r.sa).concat(n.map(r=>r.ya)),r=>{var f={};return n.forEach((e,a)=>{var i=r[a],t=e.qa,b=e.ra,o=r[a+n.length],k=e.xa,c=e.za;f[e.na]={read:r=>i.fromWireType(t(b,r)),write:(r,e)=>{var a=[];k(c,r,o.toWireType(a,e)),rA(a)}}}),[{name:e.name,fromWireType:function(r){var e,a={};for(e in f)a[e]=f[e].read(r);return i(r),a},toWireType:function(r,e){for(var n in f)if(!(n in e))throw TypeError('Missing field:  "'+n+'"');var t=a();for(n in f)f[n].write(t,e[n]);return null!==r&&r.push(i,t),t},argPackAdvance:8,readValueFromPointer:rl,U:i}]})},w:function(){},C:function(r,e,a,i,n){var f=rp(a);rm(r,{name:e=x(e),fromWireType:function(r){return!!r},toWireType:function(r,e){return e?i:n},argPackAdvance:8,readValueFromPointer:function(r){if(1===a)var i=N;else if(2===a)i=y;else if(4===a)i=G;else throw TypeError("Unknown boolean type size: "+e);return this.fromWireType(i[r>>f])},U:null})},f:function(r,e,a,i,n,f,t,b,o,k,c,s,A){c=x(c),f=rV(n,f),b&&(b=rV(t,b)),k&&(k=rV(o,k)),A=rV(s,A);var l,d=C(c);l=function(){rT("Cannot construct "+c+" due to unbound types",[i])},u.hasOwnProperty(d)?(H("Cannot register public name '"+d+"' twice"),ry(u,d,d),u.hasOwnProperty(void 0)&&H("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),u[d].R[void 0]=l):u[d]=l,rv([r,e,a],i?[i]:[],function(e){if(e=e[0],i)var a,n=e.M,t=n.W;else t=rw.prototype;e=S(d,function(){if(Object.getPrototypeOf(this)!==o)throw new M("Use 'new' to construct "+c);if(void 0===s.X)throw new M(c+" has no accessible constructor");var r=s.X[arguments.length];if(void 0===r)throw new M("Tried to invoke ctor of "+c+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(s.X).toString()+") parameters instead!");return r.apply(this,arguments)});var o=Object.create(t,{constructor:{value:e}});e.prototype=o;var s=new rE(c,e,o,A,n,f,b,k);n=new rW(c,s,!0,!1),t=new rW(c+"*",s,!1,!1);var l=new rW(c+" const*",s,!1,!0);return rb[r]={pointerType:t,ka:l},a=e,u.hasOwnProperty(d)||rk("Replacing nonexistant public symbol"),u[d]=a,u[d].Y=void 0,[n,t,l]})},d:function(r,e,a,i,n,f,t){var b=rX(a,i);e=x(e),f=rV(n,f),rv([],[r],function(r){function i(){rT("Cannot call "+n+" due to unbound types",b)}var n=(r=r[0]).name+"."+e;e.startsWith("@@")&&(e=Symbol[e.substring(2)]);var o=r.M.constructor;return void 0===o[e]?(i.Y=a-1,o[e]=i):(ry(o,e,n),o[e].R[a-1]=i),rv([],b,function(r){return r=rI(n,[r[0],null].concat(r.slice(1)),null,f,t),void 0===o[e].R?(r.Y=a-1,o[e]=r):o[e].R[a-1]=r,[]}),[]})},p:function(r,e,a,i,n,f){0<e||O();var t=rX(e,a);n=rV(i,n),rv([],[r],function(r){var a="constructor "+(r=r[0]).name;if(void 0===r.M.X&&(r.M.X=[]),void 0!==r.M.X[e-1])throw new M("Cannot register multiple constructors with identical number of parameters ("+(e-1)+") for class '"+r.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return r.M.X[e-1]=()=>{rT("Cannot construct "+r.name+" due to unbound types",t)},rv([],t,function(i){return i.splice(1,0,null),r.M.X[e-1]=rI(a,i,null,n,f),[]}),[]})},b:function(r,e,a,i,n,f,t,b){var o=rX(a,i);e=x(e),f=rV(n,f),rv([],[r],function(r){function i(){rT("Cannot call "+n+" due to unbound types",o)}var n=(r=r[0]).name+"."+e;e.startsWith("@@")&&(e=Symbol[e.substring(2)]),b&&r.M.ia.push(e);var k=r.M.W,c=k[e];return void 0===c||void 0===c.R&&c.className!==r.name&&c.Y===a-2?(i.Y=a-2,i.className=r.name,k[e]=i):(ry(k,e,n),k[e].R[a-2]=i),rv([],o,function(i){return i=rI(n,i,r,f,t),void 0===k[e].R?(i.Y=a-2,k[e]=i):k[e].R[a-2]=i,[]}),[]})},B:function(r,e){rm(r,{name:e=x(e),fromWireType:function(r){var e=Q(r);return rO(r),e},toWireType:function(r,e){return z(e)},argPackAdvance:8,readValueFromPointer:rl,U:null})},n:function(r,e,a){a=rp(a),rm(r,{name:e=x(e),fromWireType:function(r){return r},toWireType:function(r,e){return e},argPackAdvance:8,readValueFromPointer:function(r,e){switch(e){case 2:return function(r){return this.fromWireType(g[r>>2])};case 3:return function(r){return this.fromWireType(Z[r>>3])};default:throw TypeError("Unknown float type: "+r)}}(e,a),U:null})},e:function(r,e,a,i,n){e=x(e),-1===n&&(n=4294967295),n=rp(a);var f=r=>r;if(0===i){var t=32-8*a;f=r=>r<<t>>>t}a=e.includes("unsigned")?function(r,e){return e>>>0}:function(r,e){return e},rm(r,{name:e,fromWireType:f,toWireType:a,argPackAdvance:8,readValueFromPointer:function(r,e,a){switch(e){case 0:return a?function(r){return N[r]}:function(r){return w[r]};case 1:return a?function(r){return y[r>>1]}:function(r){return E[r>>1]};case 2:return a?function(r){return G[r>>2]}:function(r){return R[r>>2]};default:throw TypeError("Unknown integer type: "+r)}}(e,n,0!==i),U:null})},c:function(r,e,a){function i(r){r>>=2;var e=R;return new n(e.buffer,e[r+1],e[r])}var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][e];rm(r,{name:a=x(a),fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{ta:!0})},o:function(r,e){var a="std::string"===(e=x(e));rm(r,{name:e,fromWireType:function(r){var e=R[r>>2],i=r+4;if(a)for(var n=i,f=0;f<=e;++f){var t=i+f;if(f==e||0==w[t]){if(n=n?v(w,n,t-n):"",void 0===b)var b=n;else b+="\x00"+n;n=t+1}}else{for(f=0,b=Array(e);f<e;++f)b[f]=String.fromCharCode(w[i+f]);b=b.join("")}return r0(r),b},toWireType:function(r,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var i,n="string"==typeof e;if(n||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||H("Cannot pass non-string to std::string"),a&&n){var f=0;for(i=0;i<e.length;++i){var t=e.charCodeAt(i);127>=t?f++:2047>=t?f+=2:55296<=t&&57343>=t?(f+=4,++i):f+=3}i=f}else i=e.length;if(t=(f=r$(4+i+1))+4,R[f>>2]=i,a&&n){if(n=t,t=i+1,i=w,0<t){t=n+t-1;for(var b=0;b<e.length;++b){var o=e.charCodeAt(b);if(55296<=o&&57343>=o&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++b)),127>=o){if(n>=t)break;i[n++]=o}else{if(2047>=o){if(n+1>=t)break;i[n++]=192|o>>6}else{if(65535>=o){if(n+2>=t)break;i[n++]=224|o>>12}else{if(n+3>=t)break;i[n++]=240|o>>18,i[n++]=128|o>>12&63}i[n++]=128|o>>6&63}i[n++]=128|63&o}}i[n]=0}}else if(n)for(n=0;n<i;++n)255<(b=e.charCodeAt(n))&&(r0(t),H("String has UTF-16 code units that do not fit in 8 bits")),w[t+n]=b;else for(n=0;n<i;++n)w[t+n]=e[n];return null!==r&&r.push(r0,f),f},argPackAdvance:8,readValueFromPointer:rl,U:function(r){r0(r)}})},j:function(r,e,a){if(a=x(a),2===e)var i=r_,n=rF,f=rC,t=()=>E,b=1;else 4===e&&(i=rS,n=rP,f=rY,t=()=>R,b=2);rm(r,{name:a,fromWireType:function(r){for(var a,n=R[r>>2],f=t(),o=r+4,k=0;k<=n;++k){var c=r+4+k*e;(k==n||0==f[c>>b])&&(o=i(o,c-o),void 0===a?a=o:a+="\x00"+o,o=c+e)}return r0(r),a},toWireType:function(r,i){"string"!=typeof i&&H("Cannot pass non-string to C++ string type "+a);var t=f(i),o=r$(4+t+e);return R[o>>2]=t>>b,n(i,o+4,t+e),null!==r&&r.push(r0,o),o},argPackAdvance:8,readValueFromPointer:rl,U:function(r){r0(r)}})},l:function(r,e,a,i,n,f){rs[r]={name:x(e),ea:rV(a,i),V:rV(n,f),ha:[]}},i:function(r,e,a,i,n,f,t,b,o,k){rs[r].ha.push({na:x(e),sa:a,qa:rV(i,n),ra:f,ya:t,xa:rV(b,o),za:k})},D:function(r,e){rm(r,{ua:!0,name:e=x(e),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},t:function(r,e,a,i,n){r=rH[r],e=Q(e),a=rM(a);var f=[];return R[i>>2]=z(f),r(e,a,f,n)},u:function(r,e,a,i){r=rH[r],r(e=Q(e),a=rM(a),null,i)},h:rO,m:function(r,e){var a,i,n=function(r,e){for(var a=Array(r),i=0;i<r;++i)a[i]=ri(R[e+4*i>>2],"parameter "+i);return a}(r,e),f=n[0],t=rQ[e=f.name+"_$"+n.slice(1).map(function(r){return r.name}).join("_")+"$"];if(void 0!==t)return t;var b=Array(r-1);return a=(e,a,i,t)=>{for(var o=0,k=0;k<r-1;++k)b[k]=n[k+1].readValueFromPointer(t+o),o+=n[k+1].argPackAdvance;for(k=0,e=e[a].apply(e,b);k<r-1;++k)n[k+1].la&&n[k+1].la(b[k]);if(!f.ua)return f.toWireType(i,e)},i=rH.length,rH.push(a),t=i,rQ[e]=t},r:function(r){4<r&&(P[r].fa+=1)},s:function(r){rA(Q(r)),rO(r)},g:function(){O("")},y:function(r,e,a){w.copyWithin(r,e,e+a)},x:function(r){var e=w.length;if(2147483648<(r>>>=0))return!1;for(var a=1;4>=a;a*=2){var i=e*(1+.2/a);i=Math.min(i,r+100663296);var n=Math,f=n.min;i=Math.max(r,i),i+=(65536-i%65536)%65536;r:{var t=d.buffer;try{d.grow(f.call(n,2147483648,i)-t.byteLength+65535>>>16),p();var b=1;break r}catch(r){}b=void 0}if(b)return!0}return!1},A:function(){return 52},v:function(){return 70},z:function(r,e,a,i){for(var n=0,f=0;f<a;f++){var o=R[e>>2],k=R[e+4>>2];e+=8;for(var c=0;c<k;c++){var u=w[o+c],s=rz[r];0===u||10===u?((1===r?t:b)(v(s,0)),s.length=0):s.push(u)}n+=k}return R[i>>2]=n,0},a:d},rK=function(){function r(r){u.asm=r.exports,W=u.asm.I,B.unshift(u.asm.E),I--,u.monitorRunDependencies&&u.monitorRunDependencies(I),0==I&&X&&(r=X,X=null,r())}var e={a:rJ};if(I++,u.monitorRunDependencies&&u.monitorRunDependencies(I),u.instantiateWasm)try{return u.instantiateWasm(e,r)}catch(r){b("Module.instantiateWasm callback failed with error: "+r),A(r)}return e=function(r){var e=a;try{try{if(e==a&&l)new Uint8Array(l);else{if(L(e)){try{var i=rx(e.slice(37)),n=new Uint8Array(i.length);for(e=0;e<i.length;++e)n[e]=i.charCodeAt(e);var f=n}catch(r){throw Error("Converting base64 string to bytes failed.")}var t=f}else t=void 0;if(!t)throw"sync fetching of the wasm failed: you can preload it to Module['wasmBinary'] manually, or emcc.py will do that for you when generating HTML (but not JS)"}}catch(r){O(r)}var c=new o,u=new k(r)}catch(e){throw b("failed to compile wasm module: "+(r=e.toString())),(r.includes("imported Memory")||r.includes("memory import"))&&b("Memory size incompatibility issues may be due to changing INITIAL_MEMORY at runtime to something too large. Use ALLOW_MEMORY_GROWTH to allow any size memory (and also make sure not to set INITIAL_MEMORY at runtime to something smaller than it was at compile time)."),e}return[u,c]}(e),r(e[0]),u.asm}(),rq=u.___getTypeName=rK.F;u.__embind_initialize_bindings=rK.G;var r$=rK.H,r0=rK.J;function r2(){function r(){if(!i&&(i=!0,u.calledRun=!0,!h)){if(F(B),s(u),u.onRuntimeInitialized&&u.onRuntimeInitialized(),u.postRun)for("function"==typeof u.postRun&&(u.postRun=[u.postRun]);u.postRun.length;){var r=u.postRun.shift();T.unshift(r)}F(T)}}if(!(0<I)){if(u.preRun)for("function"==typeof u.preRun&&(u.preRun=[u.preRun]);u.preRun.length;)!function(){var r=u.preRun.shift();V.unshift(r)}();F(V),0<I||(u.setStatus?(u.setStatus("Running..."),setTimeout(function(){setTimeout(function(){u.setStatus("")},1),r()},1)):r())}}if(u.dynCall_jiji=rK.K,X=function r(){i||r2(),i||(X=r)},u.preInit)for("function"==typeof u.preInit&&(u.preInit=[u.preInit]);0<u.preInit.length;)u.preInit.pop()();return r2(),e}})();function asm(){return r(yoga())}export{asm as default};
