/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py

type ALIGN_AUTO = 0 & ['ALIGN']
export const ALIGN_AUTO: ALIGN_AUTO;

type <PERSON>IGN_FLEX_START = 1 & ['ALIGN']
export const ALIGN_FLEX_START: ALIGN_FLEX_START;

type ALIGN_CENTER = 2 & ['ALIGN']
export const ALIGN_CENTER: ALIGN_CENTER;

type ALIGN_FLEX_END = 3 & ['ALIGN']
export const ALIGN_FLEX_END: ALIGN_FLEX_END;

type ALIGN_STRETCH = 4 & ['ALIGN']
export const ALIGN_STRETCH: ALIGN_STRETCH;

type ALIGN_BASELINE = 5 & ['ALIGN']
export const ALIGN_BASELINE: ALIGN_BASELINE;

type <PERSON>IGN_SPACE_BETWEEN = 6 & ['ALIGN']
export const ALIGN_SPACE_BETWEEN: ALIGN_SPACE_BETWEEN;

type ALIGN_SPACE_AROUND = 7 & ['ALIGN']
export const ALIGN_SPACE_AROUND: ALIGN_SPACE_AROUND;


type DIMENSION_WIDTH = 0 & ['DIMENSION']
export const DIMENSION_WIDTH: DIMENSION_WIDTH;

type DIMENSION_HEIGHT = 1 & ['DIMENSION']
export const DIMENSION_HEIGHT: DIMENSION_HEIGHT;


type DIRECTION_INHERIT = 0 & ['DIRECTION']
export const DIRECTION_INHERIT: DIRECTION_INHERIT;

type DIRECTION_LTR = 1 & ['DIRECTION']
export const DIRECTION_LTR: DIRECTION_LTR;

type DIRECTION_RTL = 2 & ['DIRECTION']
export const DIRECTION_RTL: DIRECTION_RTL;


type DISPLAY_FLEX = 0 & ['DISPLAY']
export const DISPLAY_FLEX: DISPLAY_FLEX;

type DISPLAY_NONE = 1 & ['DISPLAY']
export const DISPLAY_NONE: DISPLAY_NONE;


type EDGE_LEFT = 0 & ['EDGE']
export const EDGE_LEFT: EDGE_LEFT;

type EDGE_TOP = 1 & ['EDGE']
export const EDGE_TOP: EDGE_TOP;

type EDGE_RIGHT = 2 & ['EDGE']
export const EDGE_RIGHT: EDGE_RIGHT;

type EDGE_BOTTOM = 3 & ['EDGE']
export const EDGE_BOTTOM: EDGE_BOTTOM;

type EDGE_START = 4 & ['EDGE']
export const EDGE_START: EDGE_START;

type EDGE_END = 5 & ['EDGE']
export const EDGE_END: EDGE_END;

type EDGE_HORIZONTAL = 6 & ['EDGE']
export const EDGE_HORIZONTAL: EDGE_HORIZONTAL;

type EDGE_VERTICAL = 7 & ['EDGE']
export const EDGE_VERTICAL: EDGE_VERTICAL;

type EDGE_ALL = 8 & ['EDGE']
export const EDGE_ALL: EDGE_ALL;


type EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS = 0 & ['EXPERIMENTAL_FEATURE']
export const EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS: EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS;

type EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE = 1 & ['EXPERIMENTAL_FEATURE']
export const EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE: EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE;

type EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN = 2 & ['EXPERIMENTAL_FEATURE']
export const EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN: EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN;


type FLEX_DIRECTION_COLUMN = 0 & ['FLEX_DIRECTION']
export const FLEX_DIRECTION_COLUMN: FLEX_DIRECTION_COLUMN;

type FLEX_DIRECTION_COLUMN_REVERSE = 1 & ['FLEX_DIRECTION']
export const FLEX_DIRECTION_COLUMN_REVERSE: FLEX_DIRECTION_COLUMN_REVERSE;

type FLEX_DIRECTION_ROW = 2 & ['FLEX_DIRECTION']
export const FLEX_DIRECTION_ROW: FLEX_DIRECTION_ROW;

type FLEX_DIRECTION_ROW_REVERSE = 3 & ['FLEX_DIRECTION']
export const FLEX_DIRECTION_ROW_REVERSE: FLEX_DIRECTION_ROW_REVERSE;


type GUTTER_COLUMN = 0 & ['GUTTER']
export const GUTTER_COLUMN: GUTTER_COLUMN;

type GUTTER_ROW = 1 & ['GUTTER']
export const GUTTER_ROW: GUTTER_ROW;

type GUTTER_ALL = 2 & ['GUTTER']
export const GUTTER_ALL: GUTTER_ALL;


type JUSTIFY_FLEX_START = 0 & ['JUSTIFY']
export const JUSTIFY_FLEX_START: JUSTIFY_FLEX_START;

type JUSTIFY_CENTER = 1 & ['JUSTIFY']
export const JUSTIFY_CENTER: JUSTIFY_CENTER;

type JUSTIFY_FLEX_END = 2 & ['JUSTIFY']
export const JUSTIFY_FLEX_END: JUSTIFY_FLEX_END;

type JUSTIFY_SPACE_BETWEEN = 3 & ['JUSTIFY']
export const JUSTIFY_SPACE_BETWEEN: JUSTIFY_SPACE_BETWEEN;

type JUSTIFY_SPACE_AROUND = 4 & ['JUSTIFY']
export const JUSTIFY_SPACE_AROUND: JUSTIFY_SPACE_AROUND;

type JUSTIFY_SPACE_EVENLY = 5 & ['JUSTIFY']
export const JUSTIFY_SPACE_EVENLY: JUSTIFY_SPACE_EVENLY;


type LOG_LEVEL_ERROR = 0 & ['LOG_LEVEL']
export const LOG_LEVEL_ERROR: LOG_LEVEL_ERROR;

type LOG_LEVEL_WARN = 1 & ['LOG_LEVEL']
export const LOG_LEVEL_WARN: LOG_LEVEL_WARN;

type LOG_LEVEL_INFO = 2 & ['LOG_LEVEL']
export const LOG_LEVEL_INFO: LOG_LEVEL_INFO;

type LOG_LEVEL_DEBUG = 3 & ['LOG_LEVEL']
export const LOG_LEVEL_DEBUG: LOG_LEVEL_DEBUG;

type LOG_LEVEL_VERBOSE = 4 & ['LOG_LEVEL']
export const LOG_LEVEL_VERBOSE: LOG_LEVEL_VERBOSE;

type LOG_LEVEL_FATAL = 5 & ['LOG_LEVEL']
export const LOG_LEVEL_FATAL: LOG_LEVEL_FATAL;


type MEASURE_MODE_UNDEFINED = 0 & ['MEASURE_MODE']
export const MEASURE_MODE_UNDEFINED: MEASURE_MODE_UNDEFINED;

type MEASURE_MODE_EXACTLY = 1 & ['MEASURE_MODE']
export const MEASURE_MODE_EXACTLY: MEASURE_MODE_EXACTLY;

type MEASURE_MODE_AT_MOST = 2 & ['MEASURE_MODE']
export const MEASURE_MODE_AT_MOST: MEASURE_MODE_AT_MOST;


type NODE_TYPE_DEFAULT = 0 & ['NODE_TYPE']
export const NODE_TYPE_DEFAULT: NODE_TYPE_DEFAULT;

type NODE_TYPE_TEXT = 1 & ['NODE_TYPE']
export const NODE_TYPE_TEXT: NODE_TYPE_TEXT;


type OVERFLOW_VISIBLE = 0 & ['OVERFLOW']
export const OVERFLOW_VISIBLE: OVERFLOW_VISIBLE;

type OVERFLOW_HIDDEN = 1 & ['OVERFLOW']
export const OVERFLOW_HIDDEN: OVERFLOW_HIDDEN;

type OVERFLOW_SCROLL = 2 & ['OVERFLOW']
export const OVERFLOW_SCROLL: OVERFLOW_SCROLL;


type POSITION_TYPE_STATIC = 0 & ['POSITION_TYPE']
export const POSITION_TYPE_STATIC: POSITION_TYPE_STATIC;

type POSITION_TYPE_RELATIVE = 1 & ['POSITION_TYPE']
export const POSITION_TYPE_RELATIVE: POSITION_TYPE_RELATIVE;

type POSITION_TYPE_ABSOLUTE = 2 & ['POSITION_TYPE']
export const POSITION_TYPE_ABSOLUTE: POSITION_TYPE_ABSOLUTE;


type PRINT_OPTIONS_LAYOUT = 1 & ['PRINT_OPTIONS']
export const PRINT_OPTIONS_LAYOUT: PRINT_OPTIONS_LAYOUT;

type PRINT_OPTIONS_STYLE = 2 & ['PRINT_OPTIONS']
export const PRINT_OPTIONS_STYLE: PRINT_OPTIONS_STYLE;

type PRINT_OPTIONS_CHILDREN = 4 & ['PRINT_OPTIONS']
export const PRINT_OPTIONS_CHILDREN: PRINT_OPTIONS_CHILDREN;


type UNIT_UNDEFINED = 0 & ['UNIT']
export const UNIT_UNDEFINED: UNIT_UNDEFINED;

type UNIT_POINT = 1 & ['UNIT']
export const UNIT_POINT: UNIT_POINT;

type UNIT_PERCENT = 2 & ['UNIT']
export const UNIT_PERCENT: UNIT_PERCENT;

type UNIT_AUTO = 3 & ['UNIT']
export const UNIT_AUTO: UNIT_AUTO;


type WRAP_NO_WRAP = 0 & ['WRAP']
export const WRAP_NO_WRAP: WRAP_NO_WRAP;

type WRAP_WRAP = 1 & ['WRAP']
export const WRAP_WRAP: WRAP_WRAP;

type WRAP_WRAP_REVERSE = 2 & ['WRAP']
export const WRAP_WRAP_REVERSE: WRAP_WRAP_REVERSE;


export type Align =
  | typeof ALIGN_AUTO
  | typeof ALIGN_FLEX_START
  | typeof ALIGN_CENTER
  | typeof ALIGN_FLEX_END
  | typeof ALIGN_STRETCH
  | typeof ALIGN_BASELINE
  | typeof ALIGN_SPACE_BETWEEN
  | typeof ALIGN_SPACE_AROUND;

export type Dimension =
  | typeof DIMENSION_WIDTH
  | typeof DIMENSION_HEIGHT;

export type Direction =
  | typeof DIRECTION_INHERIT
  | typeof DIRECTION_LTR
  | typeof DIRECTION_RTL;

export type Display =
  | typeof DISPLAY_FLEX
  | typeof DISPLAY_NONE;

export type Edge =
  | typeof EDGE_LEFT
  | typeof EDGE_TOP
  | typeof EDGE_RIGHT
  | typeof EDGE_BOTTOM
  | typeof EDGE_START
  | typeof EDGE_END
  | typeof EDGE_HORIZONTAL
  | typeof EDGE_VERTICAL
  | typeof EDGE_ALL;

export type ExperimentalFeature =
  | typeof EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS
  | typeof EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE
  | typeof EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN;

export type FlexDirection =
  | typeof FLEX_DIRECTION_COLUMN
  | typeof FLEX_DIRECTION_COLUMN_REVERSE
  | typeof FLEX_DIRECTION_ROW
  | typeof FLEX_DIRECTION_ROW_REVERSE;

export type Gutter =
  | typeof GUTTER_COLUMN
  | typeof GUTTER_ROW
  | typeof GUTTER_ALL;

export type Justify =
  | typeof JUSTIFY_FLEX_START
  | typeof JUSTIFY_CENTER
  | typeof JUSTIFY_FLEX_END
  | typeof JUSTIFY_SPACE_BETWEEN
  | typeof JUSTIFY_SPACE_AROUND
  | typeof JUSTIFY_SPACE_EVENLY;

export type LogLevel =
  | typeof LOG_LEVEL_ERROR
  | typeof LOG_LEVEL_WARN
  | typeof LOG_LEVEL_INFO
  | typeof LOG_LEVEL_DEBUG
  | typeof LOG_LEVEL_VERBOSE
  | typeof LOG_LEVEL_FATAL;

export type MeasureMode =
  | typeof MEASURE_MODE_UNDEFINED
  | typeof MEASURE_MODE_EXACTLY
  | typeof MEASURE_MODE_AT_MOST;

export type NodeType =
  | typeof NODE_TYPE_DEFAULT
  | typeof NODE_TYPE_TEXT;

export type Overflow =
  | typeof OVERFLOW_VISIBLE
  | typeof OVERFLOW_HIDDEN
  | typeof OVERFLOW_SCROLL;

export type PositionType =
  | typeof POSITION_TYPE_STATIC
  | typeof POSITION_TYPE_RELATIVE
  | typeof POSITION_TYPE_ABSOLUTE;

export type PrintOptions =
  | typeof PRINT_OPTIONS_LAYOUT
  | typeof PRINT_OPTIONS_STYLE
  | typeof PRINT_OPTIONS_CHILDREN;

export type Unit =
  | typeof UNIT_UNDEFINED
  | typeof UNIT_POINT
  | typeof UNIT_PERCENT
  | typeof UNIT_AUTO;

export type Wrap =
  | typeof WRAP_NO_WRAP
  | typeof WRAP_WRAP
  | typeof WRAP_WRAP_REVERSE;

