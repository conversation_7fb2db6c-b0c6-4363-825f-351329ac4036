var YGEnums={},ALIGN_AUTO=YGEnums.ALIGN_AUTO=0,ALIGN_FLEX_START=YGEnums.ALIGN_FLEX_START=1,ALIGN_CENTER=YGEnums.ALIGN_CENTER=2,ALIGN_FLEX_END=YGEnums.ALIGN_FLEX_END=3,ALIGN_STRETCH=YGEnums.ALIGN_STRETCH=4,ALIGN_BASELINE=YGEnums.ALIGN_BASELINE=5,ALIGN_SPACE_BETWEEN=YGEnums.ALIGN_SPACE_BETWEEN=6,ALIGN_SPACE_AROUND=YGEnums.ALIGN_SPACE_AROUND=7,DIMENSION_WIDTH=YGEnums.DIMENSION_WIDTH=0,DIMENSION_HEIGHT=YGEnums.DIMENSION_HEIGHT=1,DIRECTION_INHERIT=YGEnums.DIRECTION_INHERIT=0,DIRECTION_LTR=YGEnums.DIRECTION_LTR=1,DIRECTION_RTL=YGEnums.DIRECTION_RTL=2,DISPLAY_FLEX=YGEnums.DISPLAY_FLEX=0,DISPLAY_NONE=YGEnums.DISPLAY_NONE=1,EDGE_LEFT=YGEnums.EDGE_LEFT=0,EDGE_TOP=YGEnums.EDGE_TOP=1,EDGE_RIGHT=YGEnums.EDGE_RIGHT=2,EDGE_BOTTOM=YGEnums.EDGE_BOTTOM=3,EDGE_START=YGEnums.EDGE_START=4,EDGE_END=YGEnums.EDGE_END=5,EDGE_HORIZONTAL=YGEnums.EDGE_HORIZONTAL=6,EDGE_VERTICAL=YGEnums.EDGE_VERTICAL=7,EDGE_ALL=YGEnums.EDGE_ALL=8,EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS=YGEnums.EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS=0,EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE=YGEnums.EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE=1,EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN=YGEnums.EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN=2,FLEX_DIRECTION_COLUMN=YGEnums.FLEX_DIRECTION_COLUMN=0,FLEX_DIRECTION_COLUMN_REVERSE=YGEnums.FLEX_DIRECTION_COLUMN_REVERSE=1,FLEX_DIRECTION_ROW=YGEnums.FLEX_DIRECTION_ROW=2,FLEX_DIRECTION_ROW_REVERSE=YGEnums.FLEX_DIRECTION_ROW_REVERSE=3,GUTTER_COLUMN=YGEnums.GUTTER_COLUMN=0,GUTTER_ROW=YGEnums.GUTTER_ROW=1,GUTTER_ALL=YGEnums.GUTTER_ALL=2,JUSTIFY_FLEX_START=YGEnums.JUSTIFY_FLEX_START=0,JUSTIFY_CENTER=YGEnums.JUSTIFY_CENTER=1,JUSTIFY_FLEX_END=YGEnums.JUSTIFY_FLEX_END=2,JUSTIFY_SPACE_BETWEEN=YGEnums.JUSTIFY_SPACE_BETWEEN=3,JUSTIFY_SPACE_AROUND=YGEnums.JUSTIFY_SPACE_AROUND=4,JUSTIFY_SPACE_EVENLY=YGEnums.JUSTIFY_SPACE_EVENLY=5,LOG_LEVEL_ERROR=YGEnums.LOG_LEVEL_ERROR=0,LOG_LEVEL_WARN=YGEnums.LOG_LEVEL_WARN=1,LOG_LEVEL_INFO=YGEnums.LOG_LEVEL_INFO=2,LOG_LEVEL_DEBUG=YGEnums.LOG_LEVEL_DEBUG=3,LOG_LEVEL_VERBOSE=YGEnums.LOG_LEVEL_VERBOSE=4,LOG_LEVEL_FATAL=YGEnums.LOG_LEVEL_FATAL=5,MEASURE_MODE_UNDEFINED=YGEnums.MEASURE_MODE_UNDEFINED=0,MEASURE_MODE_EXACTLY=YGEnums.MEASURE_MODE_EXACTLY=1,MEASURE_MODE_AT_MOST=YGEnums.MEASURE_MODE_AT_MOST=2,NODE_TYPE_DEFAULT=YGEnums.NODE_TYPE_DEFAULT=0,NODE_TYPE_TEXT=YGEnums.NODE_TYPE_TEXT=1,OVERFLOW_VISIBLE=YGEnums.OVERFLOW_VISIBLE=0,OVERFLOW_HIDDEN=YGEnums.OVERFLOW_HIDDEN=1,OVERFLOW_SCROLL=YGEnums.OVERFLOW_SCROLL=2,POSITION_TYPE_STATIC=YGEnums.POSITION_TYPE_STATIC=0,POSITION_TYPE_RELATIVE=YGEnums.POSITION_TYPE_RELATIVE=1,POSITION_TYPE_ABSOLUTE=YGEnums.POSITION_TYPE_ABSOLUTE=2,PRINT_OPTIONS_LAYOUT=YGEnums.PRINT_OPTIONS_LAYOUT=1,PRINT_OPTIONS_STYLE=YGEnums.PRINT_OPTIONS_STYLE=2,PRINT_OPTIONS_CHILDREN=YGEnums.PRINT_OPTIONS_CHILDREN=4,UNIT_UNDEFINED=YGEnums.UNIT_UNDEFINED=0,UNIT_POINT=YGEnums.UNIT_POINT=1,UNIT_PERCENT=YGEnums.UNIT_PERCENT=2,UNIT_AUTO=YGEnums.UNIT_AUTO=3,WRAP_NO_WRAP=YGEnums.WRAP_NO_WRAP=0,WRAP_WRAP=YGEnums.WRAP_WRAP=1,WRAP_WRAP_REVERSE=YGEnums.WRAP_WRAP_REVERSE=2;let CONSTANTS=YGEnums;var wrapAsm=E=>{function _(E,_,T){let N=E[_];E[_]=function(...E){return T.call(this,N,...E)}}for(let T of["setPosition","setMargin","setFlexBasis","setWidth","setHeight","setMinWidth","setMinHeight","setMaxWidth","setMaxHeight","setPadding"]){let N={[YGEnums.UNIT_POINT]:E.Node.prototype[T],[YGEnums.UNIT_PERCENT]:E.Node.prototype[`${T}Percent`],[YGEnums.UNIT_AUTO]:E.Node.prototype[`${T}Auto`]};_(E.Node.prototype,T,function(E,..._){let I,L;let O=_.pop();if("auto"===O)I=YGEnums.UNIT_AUTO,L=void 0;else if("object"==typeof O)I=O.unit,L=O.valueOf();else if(I="string"==typeof O&&O.endsWith("%")?YGEnums.UNIT_PERCENT:YGEnums.UNIT_POINT,L=parseFloat(O),!Number.isNaN(O)&&Number.isNaN(L))throw Error(`Invalid value ${O} for ${T}`);if(!N[I])throw Error(`Failed to execute "${T}": Unsupported unit '${O}'`);return void 0!==L?N[I].call(this,..._,L):N[I].call(this,..._)})}function T(_){return E.MeasureCallback.implement({measure:(...E)=>{let{width:T,height:N}=_(...E);return{width:T??NaN,height:N??NaN}}})}function N(_){return E.DirtiedCallback.implement({dirtied:_})}return _(E.Node.prototype,"setMeasureFunc",function(E,_){return _?E.call(this,T(_)):this.unsetMeasureFunc()}),_(E.Node.prototype,"setDirtiedFunc",function(E,_){E.call(this,N(_))}),_(E.Config.prototype,"free",function(){E.Config.destroy(this)}),_(E.Node,"create",(_,T)=>T?E.Node.createWithConfig(T):E.Node.createDefault()),_(E.Node.prototype,"free",function(){E.Node.destroy(this)}),_(E.Node.prototype,"freeRecursive",function(){for(let E=0,_=this.getChildCount();E<_;++E)this.getChild(0).freeRecursive();this.free()}),_(E.Node.prototype,"calculateLayout",function(E,_=NaN,T=NaN,N=YGEnums.DIRECTION_LTR){return E.call(this,_,T,N)}),{Config:E.Config,Node:E.Node,...YGEnums}};export{OVERFLOW_HIDDEN as $,ALIGN_AUTO as A,FLEX_DIRECTION_ROW as B,FLEX_DIRECTION_ROW_REVERSE as C,DIMENSION_WIDTH as D,EDGE_LEFT as E,FLEX_DIRECTION_COLUMN as F,GUTTER_COLUMN as G,GUTTER_ROW as H,GUTTER_ALL as I,JUSTIFY_FLEX_START as J,JUSTIFY_CENTER as K,JUSTIFY_FLEX_END as L,JUSTIFY_SPACE_BETWEEN as M,JUSTIFY_SPACE_AROUND as N,JUSTIFY_SPACE_EVENLY as O,LOG_LEVEL_ERROR as P,LOG_LEVEL_WARN as Q,LOG_LEVEL_INFO as R,LOG_LEVEL_DEBUG as S,LOG_LEVEL_VERBOSE as T,LOG_LEVEL_FATAL as U,MEASURE_MODE_UNDEFINED as V,MEASURE_MODE_EXACTLY as W,MEASURE_MODE_AT_MOST as X,NODE_TYPE_DEFAULT as Y,NODE_TYPE_TEXT as Z,OVERFLOW_VISIBLE as _,ALIGN_FLEX_START as a,OVERFLOW_SCROLL as a0,POSITION_TYPE_STATIC as a1,POSITION_TYPE_RELATIVE as a2,POSITION_TYPE_ABSOLUTE as a3,PRINT_OPTIONS_LAYOUT as a4,PRINT_OPTIONS_STYLE as a5,PRINT_OPTIONS_CHILDREN as a6,UNIT_UNDEFINED as a7,UNIT_POINT as a8,UNIT_PERCENT as a9,UNIT_AUTO as aa,WRAP_NO_WRAP as ab,WRAP_WRAP as ac,WRAP_WRAP_REVERSE as ad,ALIGN_CENTER as b,ALIGN_FLEX_END as c,ALIGN_STRETCH as d,ALIGN_BASELINE as e,ALIGN_SPACE_BETWEEN as f,ALIGN_SPACE_AROUND as g,DIMENSION_HEIGHT as h,DIRECTION_INHERIT as i,DIRECTION_LTR as j,DIRECTION_RTL as k,DISPLAY_FLEX as l,DISPLAY_NONE as m,EDGE_TOP as n,EDGE_RIGHT as o,EDGE_BOTTOM as p,EDGE_START as q,EDGE_END as r,EDGE_HORIZONTAL as s,EDGE_VERTICAL as t,EDGE_ALL as u,EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS as v,wrapAsm as w,EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE as x,EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN as y,FLEX_DIRECTION_COLUMN_REVERSE as z};
