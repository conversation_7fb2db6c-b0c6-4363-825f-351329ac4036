var E={},a=E.ALIGN_AUTO=0,_=E.ALIGN_FLEX_START=1,s=E.ALIGN_CENTER=2,e=E.ALIGN_FLEX_END=3,t=E.ALIGN_STRETCH=4,N=E.ALIGN_BASELINE=5,T=E.ALIGN_SPACE_BETWEEN=6,I=E.ALIGN_SPACE_AROUND=7,o=E.DIMENSION_WIDTH=0,L=E.DIMENSION_HEIGHT=1,O=E.DIRECTION_INHERIT=0,R=E.DIRECTION_LTR=1,i=E.DIRECTION_RTL=2,r=E.DISPLAY_FLEX=0,A=E.DISPLAY_NONE=1,n=E.EDGE_LEFT=0,D=E.EDGE_TOP=1,S=E.EDGE_RIGHT=2,U=E.EDGE_BOTTOM=3,P=E.EDGE_START=4,u=E.EDGE_END=5,C=E.EDGE_HORIZONTAL=6,c=E.EDGE_VERTICAL=7,d=E.EDGE_ALL=8,F=E.EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS=0,G=E.EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE=1,l=E.EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN=2,f=E.FLEX_DIRECTION_COLUMN=0,h=E.FLEX_DIRECTION_COLUMN_REVERSE=1,p=E.FLEX_DIRECTION_ROW=2,M=E.FLEX_DIRECTION_ROW_REVERSE=3,W=E.GUTTER_COLUMN=0,V=E.GUTTER_ROW=1,Y=E.GUTTER_ALL=2,X=E.JUSTIFY_FLEX_START=0,g=E.JUSTIFY_CENTER=1,y=E.JUSTIFY_FLEX_END=2,B=E.JUSTIFY_SPACE_BETWEEN=3,H=E.JUSTIFY_SPACE_AROUND=4,v=E.JUSTIFY_SPACE_EVENLY=5,m=E.LOG_LEVEL_ERROR=0,b=E.LOG_LEVEL_WARN=1,w=E.LOG_LEVEL_INFO=2,J=E.LOG_LEVEL_DEBUG=3,$=E.LOG_LEVEL_VERBOSE=4,x=E.LOG_LEVEL_FATAL=5,k=E.MEASURE_MODE_UNDEFINED=0,j=E.MEASURE_MODE_EXACTLY=1,Z=E.MEASURE_MODE_AT_MOST=2,q=E.NODE_TYPE_DEFAULT=0,z=E.NODE_TYPE_TEXT=1,K=E.OVERFLOW_VISIBLE=0,Q=E.OVERFLOW_HIDDEN=1,EE=E.OVERFLOW_SCROLL=2,aE=E.POSITION_TYPE_STATIC=0,_E=E.POSITION_TYPE_RELATIVE=1,sE=E.POSITION_TYPE_ABSOLUTE=2,eE=E.PRINT_OPTIONS_LAYOUT=1,tE=E.PRINT_OPTIONS_STYLE=2,NE=E.PRINT_OPTIONS_CHILDREN=4,TE=E.UNIT_UNDEFINED=0,IE=E.UNIT_POINT=1,oE=E.UNIT_PERCENT=2,LE=E.UNIT_AUTO=3,OE=E.WRAP_NO_WRAP=0,RE=E.WRAP_WRAP=1,iE=E.WRAP_WRAP_REVERSE=2;const rE=E;var AE=E=>{function a(E,a,_){const s=E[a];E[a]=function(...E){return _.call(this,s,...E)}}for(const _ of["setPosition","setMargin","setFlexBasis","setWidth","setHeight","setMinWidth","setMinHeight","setMaxWidth","setMaxHeight","setPadding"]){const s={[rE.UNIT_POINT]:E.Node.prototype[_],[rE.UNIT_PERCENT]:E.Node.prototype[`${_}Percent`],[rE.UNIT_AUTO]:E.Node.prototype[`${_}Auto`]};a(E.Node.prototype,_,(function(E,...a){const e=a.pop();let t,N;if("auto"===e)t=rE.UNIT_AUTO,N=void 0;else if("object"==typeof e)t=e.unit,N=e.valueOf();else if(t="string"==typeof e&&e.endsWith("%")?rE.UNIT_PERCENT:rE.UNIT_POINT,N=parseFloat(e),!Number.isNaN(e)&&Number.isNaN(N))throw new Error(`Invalid value ${e} for ${_}`);if(!s[t])throw new Error(`Failed to execute "${_}": Unsupported unit '${e}'`);return void 0!==N?s[t].call(this,...a,N):s[t].call(this,...a)}))}return a(E.Node.prototype,"setMeasureFunc",(function(a,_){return _?a.call(this,(s=_,E.MeasureCallback.implement({measure:(...E)=>{const{width:a,height:_}=s(...E);return{width:a??NaN,height:_??NaN}}}))):this.unsetMeasureFunc();var s})),a(E.Node.prototype,"setDirtiedFunc",(function(a,_){var s;a.call(this,(s=_,E.DirtiedCallback.implement({dirtied:s})))})),a(E.Config.prototype,"free",(function(){E.Config.destroy(this)})),a(E.Node,"create",((a,_)=>_?E.Node.createWithConfig(_):E.Node.createDefault())),a(E.Node.prototype,"free",(function(){E.Node.destroy(this)})),a(E.Node.prototype,"freeRecursive",(function(){for(let E=0,a=this.getChildCount();E<a;++E)this.getChild(0).freeRecursive();this.free()})),a(E.Node.prototype,"calculateLayout",(function(E,a=NaN,_=NaN,s=rE.DIRECTION_LTR){return E.call(this,a,_,s)})),{Config:E.Config,Node:E.Node,...rE}};export{Q as $,a as A,p as B,M as C,o as D,n as E,f as F,W as G,V as H,Y as I,X as J,g as K,y as L,B as M,H as N,v as O,m as P,b as Q,w as R,J as S,$ as T,x as U,k as V,j as W,Z as X,q as Y,z as Z,K as _,_ as a,EE as a0,aE as a1,_E as a2,sE as a3,eE as a4,tE as a5,NE as a6,TE as a7,IE as a8,oE as a9,LE as aa,OE as ab,RE as ac,iE as ad,s as b,e as c,t as d,N as e,T as f,I as g,L as h,O as i,R as j,i as k,r as l,A as m,D as n,S as o,U as p,P as q,u as r,C as s,c as t,d as u,F as v,AE as w,G as x,l as y,h as z};
