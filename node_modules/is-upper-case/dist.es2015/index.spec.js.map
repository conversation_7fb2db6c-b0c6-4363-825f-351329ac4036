{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC;AAEhC,IAAM,UAAU,GAAwB;IACtC,CAAC,EAAE,EAAE,KAAK,CAAC;IACX,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,MAAM,EAAE,IAAI,CAAC;IACd,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,eAAe,EAAE,IAAI,CAAC;CACxB,CAAC;AAEF,QAAQ,CAAC,eAAe,EAAE;4BACZ,KAAK,EAAE,MAAM;QACvB,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;;IAHL,KAA8B,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAA7B,IAAA,qBAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;gBAAb,KAAK,EAAE,MAAM;KAIxB;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { isUpperCase } from \".\";\n\nconst TEST_CASES: [string, boolean][] = [\n  [\"\", false],\n  [\"test\", false],\n  [\"TEST\", true],\n  [\"Test\", false],\n  [\"123\", false],\n  [\"CONSTANT_CASE\", true],\n];\n\ndescribe(\"is upper case\", () => {\n  for (const [input, result] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(isUpperCase(input)).toEqual(result);\n    });\n  }\n});\n"]}