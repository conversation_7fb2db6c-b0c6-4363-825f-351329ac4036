{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,SAAgB,WAAW,CAAC,KAAa;IACvC,OAAO,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC;AACxE,CAAC;AAFD,kCAEC", "sourcesContent": ["/**\n * Returns a boolean indicating whether the string is upper case.\n */\nexport function isUpperCase(input: string) {\n  return input.toUpperCase() === input && input.toLowerCase() !== input;\n}\n"]}