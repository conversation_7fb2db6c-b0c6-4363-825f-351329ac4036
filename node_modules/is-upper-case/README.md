# Is Upper Case

[![NPM version][npm-image]][npm-url]
[![NPM downloads][downloads-image]][downloads-url]
[![Bundle size][bundlephobia-image]][bundlephobia-url]

> Returns `true` if the string is upper case only.

## Installation

```
npm install is-upper-case --save
```

## Usage

```js
import { isUpperCase } from "is-upper-case";

isUpperCase("string"); //=> false
isUpperCase("dot.case"); //=> false
isUpperCase("PascalCase"); //=> false
isUpperCase("CONSTANT_CASE"); //=> true
```

## License

MIT

[npm-image]: https://img.shields.io/npm/v/is-upper-case.svg?style=flat
[npm-url]: https://npmjs.org/package/is-upper-case
[downloads-image]: https://img.shields.io/npm/dm/is-upper-case.svg?style=flat
[downloads-url]: https://npmjs.org/package/is-upper-case
[bundlephobia-image]: https://img.shields.io/bundlephobia/minzip/is-upper-case.svg
[bundlephobia-url]: https://bundlephobia.com/result?p=is-upper-case
