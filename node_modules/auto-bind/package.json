{"name": "auto-bind", "version": "5.0.1", "description": "Automatically bind methods to their class instance", "license": "MIT", "repository": "sindresorhus/auto-bind", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {".": "./index.js", "./react": "./react.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "react.js", "react.d.ts"], "keywords": ["auto", "bind", "class", "methods", "method", "automatically", "prototype", "instance", "function", "this", "self", "react", "component"], "devDependencies": {"@types/react": "^17.0.29", "ava": "^3.15.0", "tsd": "^0.18.0", "xo": "^0.45.0"}}